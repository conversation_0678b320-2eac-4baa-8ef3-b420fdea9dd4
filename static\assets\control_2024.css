
/* Add CSS for styling and animations */
body {
margin-top: 20px;
height: 100vh;
display: flex;
justify-content: center;
align-items: center;
background: #333; /* Dark background to highlight the glass effect */
}

#revolvingDoor {
width: 75px;
height: 300px;
position: absolute;
transform: rotateX(-10deg); /* Adjust this angle as needed for better visibility */
transform-style: preserve-3d;
perspective: 800px; /* new */

cursor: pointer;
-webkit-font-smoothing: antialiased;
}

/* .doorWrapper {
    width: 75px;
    height: 100px;
    position: absolute;
    float: left;
    margin: 0px;
    padding: 0px;
    cursor: pointer;
    -webkit-font-smoothing: antialiased;
  } */

.pane {
width: 50%;
height: 100%;
position: absolute;
border: 1px solid rgba(255, 255, 255, 0.5); /* Simulating glass borders */
background-color: rgba(255, 255, 255, 0.1); /* Glass effect */
backdrop-filter: blur(5px);
/* transform-origin: center 200px; */

transform-origin: 100% 50%; /* Set the transform origin to the right side */
/* backface-visibility: hidden; */

background-color: #1680c2;
}

.pane.selected {
    /* Example of a selected effect - can be customized */
    border: 2px solid yellow;
}


#container {
    perspective: 800px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

#pivot {
    position: relative;
    width: 400px; /* Adjust size as needed */
    height: 450px; /* Adjust height as needed */
    /* Centering the pivot */
    display: flex;
    justify-content: center;
    align-items: center;
}




/* Add the rest of your styles for .pane and keyframes animation */





#container {
    perspective: 1000px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}



#revolvingDoor.rotating {
    animation: rotate 30s infinite linear;
}
/* Adjust rotation and translation for each pane */
/* #pane1 { transform: rotateY(0deg) translateX(-50%) translateY(-50%); }
#pane2 { transform: rotateY(72deg) translateX(-50%) translateY(-50%); }
#pane3 { transform: rotateY(144deg) translateX(-50%) translateY(-50%); }
#pane4 { transform: rotateY(216deg) translateX(-50%) translateY(-50%); }
#pane5 { transform: rotateY(288deg) translateX(-50%) translateY(-50%); } */

#pane1 { transform: rotateY(0deg); }
#pane2 { transform: rotateY(-72deg); }
#pane3 { transform: rotateY(-144deg); }
#pane4 { transform: rotateY(-216deg); }
#pane5 { transform: rotateY(-288deg); }
/* background: linear-gradient(160deg, rgb(51, 241, 172), rgb(16, 11, 62)); */
#pane1 { background: linear-gradient(to right, rgba(51, 241, 172,0.1), transparent); }
#pane2 { background: linear-gradient(to bottom, rgba(51, 241, 172,0.1), transparent); }
#pane3 { background: linear-gradient(to left, rgba(51, 241, 172,0.1), transparent); }
#pane4 { background: linear-gradient(to top, rgba(51, 241, 172,0.1), transparent); }
#pane5 { background: linear-gradient(to right, transparent, rgba(51, 241, 172,0.1)); }






@keyframes rotate {
    from { transform: rotateY(0deg); }
    to { transform: rotateY(360deg); }
}
