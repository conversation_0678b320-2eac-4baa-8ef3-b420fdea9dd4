Product Requirement Document (PRD) - Performance Tracking Web App
1. Introduction
This document outlines the product requirements for a web application designed to track staff and company performance. The system aims to provide an objective and transparent performance appraisal process, align individual tasks with organizational goals, foster initiative through self-generated projects, and identify workload imbalances.

2. Goals
To create a fair and transparent performance management system.
To ensure all work undertaken is aligned with organizational, divisional, and unit objectives.
To empower staff to initiate projects that contribute to organizational goals.
To provide tools for tracking progress on tasks and Key Performance Actions (KPAs).
To identify and address issues of staff being overworked or underutilized.
To provide management with evidence-based insights into organizational performance against set objectives.
To reduce subjectivity in performance appraisals.
3. User Roles & Permissions
Board/Management:
Create/Edit/Delete Organizational Objectives & Symptoms.
View dashboards for organizational, divisional, and unit performance.
Review end-of-cycle evidence against symptoms.
Input management performance scores.
Access all reporting features.
Directors (Divisional Heads):
Create/Edit/Delete Divisional Objectives, mapping them to Organizational Objectives.
Assign tasks to Unit Heads or staff within their division.
Review and approve/request changes for KPA evidence submitted by their direct reports.
View dashboards for their division and its units.
Access reporting features relevant to their division.
Review and approve proposed Self-Generated Tasks originating from their division.
Unit Heads:
Create/Edit/Delete Unit Objectives, mapping them to Divisional Objectives.
Assign tasks to staff within their unit.
Review and approve/request changes for KPA evidence submitted by their unit members.
View dashboards for their unit.
Access reporting features relevant to their unit.
Review and approve proposed Self-Generated Tasks originating from their unit.
Staff (Individual Contributor):
View their assigned tasks and linked objectives.
Initiate Self-Generated Tasks.
Select team members for their Self-Generated Tasks from system recommendations.
Submit KPA evidence for their tasks.
View their own performance data, calendar heatmap, and Gantt chart of tasks.
Receive and view notifications.
HR Administrator (Super User):
Manage user accounts and roles.
Oversee system configuration.
Access all data for auditing and reporting purposes.
Manage objective structures if needed (e.g., bulk uploads, structural changes).
(As per main.py) Create staff profiles.
4. Functional Requirements
4.1. Objective Management
4.1.1. Create Objectives:
Board/Management can create Organizational Objectives with:
Title
Description
Symptom (real-world example/metric)
Target metrics for symptoms.
Directors can create Divisional Objectives with:
Title
Description
Mapping to one Organizational Objective.
Symptom/Target specific to the division if applicable.
Unit Heads can create Unit Objectives with:
Title
Description
Mapping to one Divisional Objective.
Symptom/Target specific to the unit if applicable.
Objectives should have clearly defined types: organisational, divisional, unit.
4.1.2. View Objectives:
Users can view objectives relevant to their level and below in a hierarchical/tree structure.
4.1.3. Edit/Delete Objectives:
Appropriate roles can edit or delete objectives they created.
4.1.4. Objective Linking:
Clear UI for linking objectives hierarchically.
4.2. Task Management
4.2.1. Create Assigned Tasks:
Users with assignment privileges (Directors, Unit Heads) can create tasks.
Tasks must include: Task Title, Description, Assignee(s), Link to Objective(s), Expected Start/End Dates.
The assign_task route in main.py will be used and enhanced.
4.2.2. Self-Generated Tasks (New Feature):
*******. Initiation: Any staff member can propose a new task/project.
Requires: Task Title, Detailed Description/Requirements, Link to one or more existing Organizational, Divisional, or Unit Objective(s).
Originator defines initial estimated duration and desired outcomes/KPAs.
*******. Team Formation:
The system analyzes the task requirements and suggests suitable staff members for collaboration (leveraging the existing find_best_fit_staff logic from main.py).
The originator can review recommendations and invite staff to join the task/project.
Invited staff receive a notification and can accept or decline.
*******. Approval:
Once a preliminary team is formed (or if it's an individual project), the self-generated task proposal is routed to the originator's Unit Head (or Divisional Director if the originator is a Unit Head) for approval.
Approvers review the task for alignment with objectives and resource availability.
*******. Activation: Upon approval, the self-generated task becomes an active task in the system, following standard KPA, evidence, and review workflows. The originator may act as the task lead or assigner for KPAs within this task.
4.2.3. Key Performance Actions (KPAs):
For each task (assigned or self-generated), define one or more KPAs.
Each KPA to include: Description, System-estimated time (or manual input), Deadline.
4.2.4. View Tasks:
Users view tasks assigned to them or initiated by them.
Managers view tasks within their teams.
4.2.5. Update Task Status:
Standard statuses: Not Started, In Progress, Pending Review, Completed, Revisions Requested.
4.3. KPA Evidence & Review Workflow
4.3.1. Submit Evidence: Staff can upload/link evidence for KPAs.
4.3.2. Draft Review Process: Assigners/Task Leads receive notifications; UI for viewing, approving, or requesting changes with comments.
4.3.3. Evidence History: Maintain a history of submissions and reviews.
4.4. Performance Visualization & Reporting
4.4.1. Calendar Heatmaps: For each staff member, based on estimated KPA times.
4.4.2. Gantt Charts: For individual tasks and project timelines.
4.4.3. Symptom Tracking Dashboard: For Board/Management to view status of organizational objective symptoms against targets.
4.4.4. Staff Performance Reports: KPA completion, deadlines, evidence quality.
4.4.5. Divisional/Unit Performance Reports: Aggregated reports.
4.4.6. Skills Matrix/Radar Chart: Integrated into employee overview, using AI-extracted skills.
4.5. End-of-Cycle Review
4.5.1. Management Scoring: Board/Management input score based on symptom improvement.
4.5.2. Performance Summary: Generate summaries incorporating KPA achievements and management score multiplier.
4.6. User Management & Authentication
4.6.1. User Registration & Login: Secure registration and login.
4.6.2. Profile Management: HR Admins manage staff profiles.
4.6.3. Role-Based Access Control (RBAC): Enforce access based on roles.
4.7. Notifications
4.7.1. In-App Notifications: For task assignments, KPA submissions, review requests, self-generated task updates (invitations, approvals), etc.
5. Non-Functional Requirements
5.1. Usability: Intuitive and user-friendly interface.
5.2. Performance: Responsive application, especially for dashboards and reports.
5.3. Scalability: Handle growing numbers of users and data.
5.4. Security: Secure authentication, authorization, and data protection.
5.5. Reliability: High availability and data integrity.
5.6. Maintainability: Well-structured and documented code.
6. System Architecture Overview (based on main.py)
Backend: Python Flask
Database: MongoDB
Frontend: HTML, CSS, JavaScript
Key Libraries/Services: Flask-PyMongo, OpenAI API, D3.js, Cal-Heatmap, jQuery.
7. Additional Planned Features
7.1. AI-Powered Insights:
Objective Suggestion: AI analyzes current organizational performance data and market trends to suggest potential new objectives or refinements to existing ones.
At-Risk Task Identification: AI monitors task progress, KPA deadlines, and resource allocation to proactively identify tasks that are at risk of delay or failure, alerting relevant managers.
Nuanced Performance Feedback: AI analyzes KPA evidence, review comments, and task outcomes to generate draft performance feedback for managers to use, highlighting strengths and areas for development.
Workload Balancing Suggestions: AI analyzes KPA estimates, actual time spent (if tracked), and task distribution to suggest reallocations or identify chronic overload/underload patterns.
7.2. Advanced Analytics & Reporting (Core Focus):
Trend Analysis: Dashboards showing performance trends over time for individuals, teams, divisions, and the organization regarding objective achievement, KPA completion, and symptom improvement.
Bottleneck Identification: Analyze task dependencies, KPA review times, and resource allocation to identify bottlenecks in workflows.
Correlation Analysis: Explore correlations between training investment, skill development, and performance outcomes.
Predictive Analytics: (Future iteration) Forecast likelihood of achieving objectives based on current progress and historical data.
Customizable Dashboards: Allow users (especially management and HR) to create custom dashboards with widgets relevant to their specific needs.
7.3. Gamification (Core Focus):
Points System: Award points for timely KPA completion, high-quality evidence, successful task completion, and contributing to objectives.
Badges/Achievements: Visual badges for milestones (e.g., "Objective Champion," "KPA Master," "Collaboration King/Queen," "Innovation Starter" for self-generated tasks).
Leaderboards: Optional leaderboards (can be by department, division, or for specific challenges/initiatives) to foster healthy competition. Consider privacy and potential negative impacts carefully.
Progress Bars & Visual Rewards: More engaging visual feedback for task and objective progress.
Challenge System: Introduce optional time-bound challenges related to specific objectives or skill development, with rewards for participation and achievement.
7.4. Mobile Accessibility:
Responsive Web Design: Ensure the web application is fully responsive and usable across various screen sizes (desktops, tablets, smartphones).
Dedicated Mobile App (Phase 2/3): Consider developing native or hybrid mobile apps for iOS and Android for enhanced user experience, push notifications, and offline capabilities (e.g., drafting KPA evidence).
7.5. 360-Degree Feedback:
Peer Feedback Module: Allow staff to request or provide feedback on specific tasks, projects, or competencies for their peers (anonymized or attributed based on configuration).
Manager-Subordinate Feedback: Structured forms for managers to provide feedback, and for subordinates to provide upward feedback on management support and objective clarity.
Integration with Appraisal: Option to incorporate summarized 360-degree feedback into the end-of-cycle performance review process.
Configurable Cycles: Allow HR to set up specific 360-degree feedback cycles with defined participants and timelines.