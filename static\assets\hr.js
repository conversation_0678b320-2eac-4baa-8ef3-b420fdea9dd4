// =================================================================================
// #region INITIALIZATION
// This is the single entry point for all JavaScript functionality on the page.
// =================================================================================


let activeSection = null;

document.addEventListener('DOMContentLoaded', function() {
    // --- Initialize UI Components ---
    initializeTabs();
    initializeKpaButtons();
    initializeTaskForms();
    initializeButtonRowNavigation();
    initializeModals();
    initializeSearch();
    initializeObjectiveManagement();
    initializeTaskApprovalButton();

    initializeGamificationSettings(); 

    // --- Initial Data Fetching ---
    fetchCurrentUser(); // Fetches user ID first
    fetchAndBuildObjectivesTree(); // Fetches data for the D3 tree
    fetchRegularObjectives(); // Populates the "Assigned Task" objective dropdown
    fetchSelfGeneratedObjectives(); // Populates the "Self-Generated" objective dropdown
    fetchNotifications();

    // --- Load Default View ---
    loadSectionContent('user-profile');
});

// #endregion

// =================================================================================
// #region INITIALIZATION FUNCTIONS
// These functions set up the primary event listeners for the page.
// =================================================================================

/**
 * Sets up the functionality for switching between "Assigned Task" and "Self-Generated Task" tabs.
 */
function initializeTabs() {
    const taskTabs = document.querySelectorAll('#task-assignment-tabs button');
    const taskContents = document.querySelectorAll('.task-content');
    if (!taskTabs.length) return;

    taskTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');
            taskTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            taskContents.forEach(content => content.classList.remove('active'));
            document.getElementById(tabId)?.classList.add('active');
        });
    });
    taskTabs[0].click(); // Activate the first tab by default
}

/**
 * Adds click listeners to the "Add KPA" buttons in both task forms.
 */
function initializeKpaButtons() {
    const kpaAssignments = {
        'add-kpa-btn-assign': 'kpa-container-assign',
        'add-kpa-btn-self': 'kpa-container-self'
    };
    Object.entries(kpaAssignments).forEach(([buttonId, containerId]) => {
        const button = document.getElementById(buttonId);
        const container = document.getElementById(containerId);
        if (button && container) {
            button.addEventListener('click', () => addKpaInputGroup(container));
        }
    });
}

/**
 * Attaches submit event listeners to the task creation forms.
 */
function initializeTaskForms() {
    const forms = {
        'assign-task-form': 'assigned',
        'self-generated-task-form': 'self-generated'
    };
    Object.entries(forms).forEach(([formId, requestType]) => {
        const form = document.getElementById(formId);
        form?.addEventListener('submit', e => {
            e.preventDefault();
            if (validateForm(form)) {
                submitTaskForm(form, requestType);
            }
        });
    });
}

/**
 * Sets up the main navigation, separating the showing of a section from the
 * running of its specific setup logic.
 */
function initializeButtonRowNavigation() {
    document.querySelectorAll('#button-row button').forEach(button => {
        button.addEventListener('click', function() {
            const sectionId = this.dataset.section;

            // This now handles hiding, tearing down the old view, and showing the new one.
            loadSectionContent(sectionId);

            // Now, run the setup logic for the newly visible section.
            if (sectionId === 'employee-overview') {
                const employeeId = sessionStorage.getItem('staffId');
                loadEmployeeData(employeeId);
            } else if (sectionId === 'objectives-tree') {
                fetchAndBuildObjectivesTree();
            }
        });
    });
}

/**
 * Initializes all modals with their open/close logic and form submission handlers.
 */
function initializeModals() {
    // Draft Submission Modal
    const draftModal = document.getElementById('draft-modal');
    draftModal?.addEventListener('click', e => { if (e.target === draftModal) closeDraftModal(); });
    document.getElementById('draft-close')?.addEventListener('click', closeDraftModal);
    document.getElementById('draft-form')?.addEventListener('submit', handleDraftFormSubmit);

    // Review Modal
    const reviewModal = document.getElementById('review-modal');
    reviewModal?.addEventListener('click', e => { if (e.target === reviewModal) closeReviewModal(); });
    document.getElementById('review-close')?.addEventListener('click', closeReviewModal);
    document.getElementById('review-form')?.addEventListener('submit', handleReviewFormSubmit);

    // Objective Creation Modal
    const objectiveModal = document.getElementById('objective-modal');
    objectiveModal?.addEventListener('click', e => { if (e.target === objectiveModal) objectiveModal.style.display = 'none'; });
    document.getElementById('add-objective-btn')?.addEventListener('click', () => { objectiveModal.style.display = 'block'; });
    document.querySelector('#objective-modal .close')?.addEventListener('click', () => { objectiveModal.style.display = 'none'; });
}

/**
 * Sets up the debounced search functionality for employees.
 */
function initializeSearch() {
    const searchInput = document.getElementById('employee-search-input');
    const searchResults = $('#employee-search-results');
    if (!searchInput) return;

    const debouncedSearch = debounce(function(searchTerm) {
        if (searchTerm.length < 2) {
            searchResults.empty().hide();
            return;
        }
        fetch(`/search_employees?search=${encodeURIComponent(searchTerm)}`)
            .then(response => response.json())
            .then(data => showSearch(data))
            .catch(handleError);
    }, 250);

    searchInput.addEventListener('input', (e) => debouncedSearch(e.target.value.trim()));

    // When a search result is clicked, show the overview and load the data. This is correct.
    searchResults.on('click', '.search-result-item', function() {
        const employeeId = $(this).data('id');
        loadSectionContent('employee-overview'); // Ensure the section is visible
        loadEmployeeData(employeeId); // Then load the data
        searchInput.value = $(this).text().trim();
        searchResults.empty().hide();
    });
}

/**
 * Sets up event listeners for creating, editing, and deleting objectives.
 */
function initializeObjectiveManagement() {
    // Handler for creating a NEW objective from the modal
    document.getElementById('create-objective-form')?.addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        
        // Construct the payload, now including the weight
        const objectiveData = {
            'title': formData.get('title'),
            'description': formData.get('description'),
            'weight': parseFloat(formData.get('weight')) || 1.0 // Get weight, default to 1.0
        };

        fetch('/objectives/add', { 
            method: 'POST', 
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(objectiveData) 
        })
        .then(handleResponse) // Use a robust response handler
        .then(data => {
            // Append the new objective to the UI
            // The backend now returns the full objective document
            appendNewObjective(data.objective); 
            
            // Close modal and reset form
            document.getElementById('objective-modal').style.display = 'none';
            this.reset();
        })
        .catch(handleError);
    });

    // Handler for deleting an objective (if you have this functionality)
    document.getElementById('objectives-container')?.addEventListener('click', function(event) {
        const deleteBtn = event.target.closest('.delete-btn');
        if (deleteBtn) {
            deleteObjective(deleteBtn.dataset.id);
        }
    });
}

/**
 * Dynamically adds the "Task Approval" button and sets up its click listener.
 */
function initializeTaskApprovalButton() {
    const buttonRow = document.getElementById('button-row');
    if (buttonRow && !buttonRow.querySelector('[data-section="task-approval"]')) {
        const approvalButton = document.createElement('button');
        approvalButton.dataset.section = 'task-approval';
        approvalButton.textContent = 'Task Approval';
        buttonRow.appendChild(approvalButton);

        approvalButton.addEventListener('click', function() {
            loadSectionContent('task-approval');
            loadTasksForApproval();
        });
    }
}

// #endregion

// =================================================================================
// #region CORE UI & DATA FUNCTIONS
// =================================================================================

/**
 * Manages content visibility, now including teardown logic for cleanup.
 * @param {string} sectionId - The ID of the section to display.
 */
function loadSectionContent(sectionId) {
    // 1. Teardown the previously active section to prevent conflicts
    if (activeSection) {
        if (activeSection === 'employee-overview') {
            teardownEmployeeOverview();
        } else if (activeSection === 'objectives-tree') {
            teardownObjectivesTree();
        }
    }

    // 2. Hide all sections
    document.querySelectorAll('.section-content').forEach(sec => {
        sec.style.display = 'none';
    });

    // 3. Show the new section and update the active state
    const sectionEl = document.getElementById(sectionId);
    if (sectionEl) {
        sectionEl.style.display = 'block';
        activeSection = sectionId; // This is the crucial update
    } else {
        console.error(`Section element not found: ${sectionId}`);
    }
}

/**
 * Fetches data and builds the D3 objectives tree.
 */
function fetchAndBuildObjectivesTree() {
    fetch('/objectives')
        .then(response => response.json())
        .then(data => {
            const processedData = processObjectivesData(data.objectives);
            createCollapsibleTree(processedData);
        })
        .catch(err => console.error('Error loading objectives tree:', err));
}

function fetchCurrentUser() {
    fetch('/get_current_user')
        .then(response => response.json())
        .then(data => {
            if (data.staff_id) {
                sessionStorage.setItem('staffId', data.staff_id);
                fetchTaskProgress(); // Now fetch progress since we have the ID.
                // loadEmployeeData(data.staff_id);
                 // Load initial data for logged-in user.
            }
        })
        .catch(handleError);
}

function fetchRegularObjectives() {
    fetch('/get_associated_objectives?task_type=regular')
        .then(response => response.json())
        .then(data => populateDropdown('associated_objectives', data.objectives))
        .catch(handleError);
}

function fetchSelfGeneratedObjectives() {
    fetch('/get_associated_objectives?task_type=self-generated')
        .then(response => response.json())
        .then(data => populateDropdown('self_associated_objectives', data.objectives))
        .catch(handleError);
}

/**
 * Fetches and displays the current user's task progress using the new animated tracker.
 * REPLACES the old fetchTaskProgress function.
 */
function fetchTaskProgress() {
    const employeeId = sessionStorage.getItem('staffId');
    if (!employeeId) return;

    fetch(`/get_employee_data/${employeeId}`)
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('animated-task-tracker-container');
            if (!container) return;
            
            container.innerHTML = ''; // Clear previous trackers

            if (!data.tasks || data.tasks.length === 0) {
                container.innerHTML = '<p>You have no assigned tasks.</p>';
                return;
            }

            // Create and append a tracker for each task
            data.tasks.forEach((task, index) => {
                const taskTrackerElement = createAnimatedTrackerHTML(task, data.staff_id);
                container.appendChild(taskTrackerElement);

                // Animate the steps for the newly added tracker
                animateKpaSteps(taskTrackerElement);
            });
        })
        .catch(handleError);
}

/**
 * Creates the HTML structure for a single animated task tracker.
 * REPLACES the old createTaskProgressHTML function.
 * @param {object} task - The task object from the backend.
 * @param {string} currentUserId - The ID of the currently logged-in user.
 * @returns {HTMLElement} - The fully constructed tracker element.
 */
function createAnimatedTrackerHTML(task, currentUserId) {
    console.log("Creating animated tracker for task:", task);
    // Create the main container for this task
    const trackerWrapper = document.createElement('div');
    trackerWrapper.className = 'task-tracker';




    

    // Add the task title
    const title = document.createElement('h4');
    title.textContent = task.task_title;
    trackerWrapper.appendChild(title);

    // Create the ordered list for the stepper
    const stepper = document.createElement('ol');
    stepper.className = 'kpa-stepper';
    
    // Create a list item for each KPA (step)
    task.progress.forEach((kpa, index) => {
        const step = document.createElement('li');
        step.className = `kpa-step ${getProgressClass(kpa.status)}`;
        
        // Store data needed for click events
        step.dataset.taskId = task.task_id;
        step.dataset.progressIndex = index;
        step.dataset.isRequester = (task.requester_id === currentUserId);

        step.innerHTML = `
            <div class="kpa-sphere">${index + 1}</div>
            <div class="kpa-label">${kpa.description.text}</div>
        `;

        // Add the click listener to open the modal
        step.addEventListener('click', () => {
            handleKpaClick(task.task_id, index, task.requester_id === currentUserId);
        });

        stepper.appendChild(step);
    });

    trackerWrapper.appendChild(stepper);
    return trackerWrapper;
}

/**
 * Animates the KPA steps of a tracker sequentially.
 * @param {HTMLElement} trackerElement - The container element for a single task tracker.
 */
function animateKpaSteps(trackerElement) {
    const steps = trackerElement.querySelectorAll('.kpa-step');
    steps.forEach((step, index) => {
        // Apply a staggered delay to the animation
        setTimeout(() => {
            step.classList.add('is-visible');
        }, (index + 1) * 300); // 300ms delay between each step
    });
}

function fetchNotifications() {
    fetch('/get_notifications')
        .then(response => response.json())
        .then(displayNotifications)
        .catch(handleError);
}



/**
 * Fetches employee data and populates visualizations.
 * MODIFIED: Now uses the SkillsRadarChart class.
 * @param {string} employeeId - The ID of the employee.
 */
function loadEmployeeData(employeeId) {
    console.log("Attempting to load data for employeeId:", employeeId);

    if (!employeeId || employeeId === "null" || employeeId === "undefined") {
        console.warn("loadEmployeeData called with invalid employeeId. Aborting fetch.");
        return;
    }

    fetch(`/get_employee_data/${employeeId}`)
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => { throw new Error(err.error || 'Network error') });
            }
            return response.json();
        })
        .then(data => {
            if (data.error) throw new Error(data.error);
            // console.log("Received employee data:", data);

            // NEW: Call the gamification renderer
            console.log("Rendering gamification visuals with data:", data);
            renderGamificationVisuals(data);
            
            // Existing chart calls
            drawSkillRadarChart(data);
            if (window.calendarHeatmap) {
                

// const taskData = [
//     { "date": "2025-07-01", "timestamp": "2025-07-01T09:00:00", "value": 1.5, "task_title": "Design Mockups", "description": "Initial design phase" },
//     { "date": "2025-07-01", "timestamp": "2025-07-01T14:30:00", "value": 2.0, "task_title": "Frontend Development", "description": "Implement the new UI" },
//     { "date": "2025-06-30", "timestamp": "2025-06-30T11:00:00", "value": 3.0, "task_title": "API Integration", "description": "Connect to the user service" }
//     // ... more task data
// ];

initFlaskCalendarHeatmap('visualizer', data.heatmap_data, {
    year: new Date().getFullYear(),
    initialPeriod: 'month' // Start with month view
});

                
                // initFlaskCalendarHeatmap(data);
            }else{
                console.log('Calendar heatmap not available. Skipping...');
            }


            
        })
    .catch(handleError);
}

// #endregion

// =================================================================================
// #region DOM MANIPULATION & DISPLAY
// =================================================================================

function addKpaInputGroup(container) {
    if (!container) return;
    const kpaInputGroup = document.createElement('div');
    kpaInputGroup.className = 'kpa-input-group';
    kpaInputGroup.innerHTML = `
        <input type="text" name="kpa_text[]" placeholder="Enter KPA" required>
        <input type="date" name="kpa_date[]" required>
        <button type="button" class="delete-kpa">&times;</button>
    `;
    container.appendChild(kpaInputGroup);
    kpaInputGroup.querySelector('.delete-kpa').addEventListener('click', () => container.removeChild(kpaInputGroup));
}

function showSearch(data) {
    const searchResults = $('#employee-search-results');
    searchResults.empty().hide();
    if (data && data.length > 0) {
        data.forEach(employee => {
            searchResults.append(`<div class="search-result-item" data-id="${employee.id}">${employee.name}</div>`);
        });
        searchResults.show();
    }
}

function populateDropdown(selectId, options) {
    const selectElement = document.getElementById(selectId);
    if (selectElement) {
        selectElement.innerHTML = '<option value="" disabled selected>Select an objective</option>';
        options.forEach(objective => {
            const option = document.createElement('option');
            option.value = objective.id;
            option.textContent = `(${objective.type}) ${objective.title}`;
            selectElement.appendChild(option);
        });
    }
}

function createTaskProgressHTML(task, currentUserId) {
    return `
        <div class="task-progress-item">
            <h4>${task.task_title}</h4>
            <div class="progress-bar-container">
                <div class="progress-bar" style="width: ${calculateProgressWidth(task.progress)}%;"></div>
            </div>
            <ul class="progress-steps">
                ${task.progress.map((kpa, index) => `
                    <li class="step ${getProgressClass(kpa.status)} ${new Date(kpa.description.date) < new Date() ? 'past-date' : ''}"
                        data-task-id="${task.task_id}"
                        data-progress-index="${index}"
                        data-is-requester="${task.requester_id === currentUserId}">
                        <label>${kpa.description.text}</label>
                        <small>Due: ${new Date(kpa.description.date).toLocaleDateString()}</small>
                    </li>
                `).join('')}
            </ul>
        </div>
    `;
}

function displayNotifications(notifications) {
    const notificationsContent = document.getElementById("notifications-content");
    if (!notificationsContent) return;
    notificationsContent.innerHTML = (!notifications || notifications.length === 0) ?
        "<p>No new notifications.</p>" :
        notifications.map(notification => `
            <div class="notification" data-id="${notification._id}">
                <p><strong>${notification.type.toUpperCase()}</strong></p>
                <p>${notification.message}</p>
                <small>${new Date(notification.created_at).toLocaleString()}</small>
            </div>
        `).join('');

    notificationsContent.querySelectorAll('.notification').forEach(el => {
        el.addEventListener('click', () => {
            markNotificationAsRead(el.dataset.id);
            el.style.opacity = "0.6";
        });
    });
}

function displayRecommendations(ranking) {
    const container = document.querySelector('.recommendations ul');
    if (!container) return;
    container.innerHTML = '';
    const oldBtn = container.parentElement.querySelector('button');
    if (oldBtn) oldBtn.remove();
    if (ranking.length === 0) {
        container.innerHTML = '<li>No suitable staff found.</li>';
        return;
    }
    ranking.forEach(staff => {
        const li = document.createElement('li');
        li.innerHTML = `
            <input type="radio" id="staff_${staff.staff_id}" name="staff_recommendation" value="${staff.staff_id}">
            <label for="staff_${staff.staff_id}">${staff.staff_id} - ${staff.match}% Match - ${staff.reasoning}</label>
        `;
        container.appendChild(li);
    });
    const submitBtn = document.createElement('button');
    submitBtn.textContent = 'Confirm & Assign Task';
    submitBtn.addEventListener('click', () => {
        const selectedRadio = document.querySelector('input[name="staff_recommendation"]:checked');
        if (selectedRadio) {
            submitSelectedStaff([selectedRadio.value]);
        } else {
            alert('Please select a staff member.');
        }
    });
    container.parentElement.appendChild(submitBtn);
}

// #endregion

// =================================================================================
// #region MODAL & WORKFLOW LOGIC
// =================================================================================

let currentTaskId = null;
let currentProgressIndex = null;

function handleKpaClick(taskId, progressIndex, isRequester) {
    if (isRequester) {
        showReviewModal(taskId, progressIndex);
    } else {
        showDraftModal(taskId, progressIndex);
    }
}

// --- Draft Modal ---
function showDraftModal(taskId, progressIndex) {
    currentTaskId = taskId;
    currentProgressIndex = progressIndex;
    document.getElementById('draft-modal').classList.add('active');
    loadExistingDrafts(taskId, progressIndex);
}

function closeDraftModal() {
    document.getElementById('draft-modal').classList.remove('active');
}

async function handleDraftFormSubmit(e) {
    e.preventDefault();
    const content = new FormData(e.target).get('draft-content');
    try {
        const response = await fetch(`/tasks/${currentTaskId}/progress/${currentProgressIndex}/submit-draft`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ content })
        });
        await handleResponse(response);
        alert('Draft submitted successfully!');
        closeDraftModal();
        fetchTaskProgress();
    } catch (error) {
        handleError(error);
    }
}

async function loadExistingDrafts(taskId, progressIndex) {
    try {
        const task = await (await fetch(`/get_task?task_id=${taskId}`)).json();
        const drafts = task.progress[progressIndex].drafts;
        const draftList = document.getElementById('draft-history');
        draftList.innerHTML = (drafts && drafts.length > 0) ?
            drafts.map(draft => `
                <div class="draft-item">
                    <p>Submitted: ${new Date(draft.submitted_at).toLocaleString()} | Status: ${draft.status}</p>
                    ${draft.comments?.length ? `<div class="comments"><strong>Comments:</strong> ${draft.comments.join(', ')}</div>` : ''}
                </div>
            `).join('') :
            '<p>No submission history for this KPA.</p>';
    } catch (error) {
        handleError(error);
    }
}

// --- Review Modal ---
function showReviewModal(taskId, progressIndex) {
    currentTaskId = taskId;
    currentProgressIndex = progressIndex;
    document.getElementById('review-modal').classList.add('active');
    loadDraftForReview(taskId, progressIndex);
}

function closeReviewModal() {
    document.getElementById('review-modal').classList.remove('active');
}

async function handleReviewFormSubmit(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    const reviewData = {
        status: formData.get('review-status'),
        comments: [formData.get('review-comments')],
        reviewer_id: sessionStorage.getItem('staffId')
    };
    try {
        const response = await fetch(`/tasks/${currentTaskId}/progress/${currentProgressIndex}/review-draft`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(reviewData)
        });
        await handleResponse(response);
        alert('Review submitted successfully!');
        closeReviewModal();
        fetchTaskProgress();
    } catch (error) {
        handleError(error);
    }
}

async function loadDraftForReview(taskId, progressIndex) {
    try {
        const task = await (await fetch(`/get_task?task_id=${taskId}`)).json();
        const kpa = task.progress[progressIndex];
        const reviewContent = document.getElementById('review-content');
        reviewContent.innerHTML = `<h3>${kpa.description.text}</h3>` +
            (kpa.drafts.map(draft => `
                <div class="draft-review">
                    <p>Submitted: ${new Date(draft.submitted_at).toLocaleString()}</p>
                    <pre>${draft.content}</pre>
                </div>
            `).join('') || '<p>No drafts submitted yet.</p>');
    } catch (error) {
        handleError(error);
    }
}

// --- Task Approval Workflow ---
function loadTasksForApproval() {
    fetch('/get_tasks_for_approval')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('tasks-for-approval-container');
            if (!container) return;
            container.innerHTML = '';
            if (data.tasks && data.tasks.length > 0) {
                const table = document.createElement('table');
                table.className = 'tasks-table';
                table.innerHTML = `
                    <thead><tr><th>Task ID</th><th>Title</th><th>Requester</th><th>Created</th><th>Objective</th><th>Actions</th></tr></thead>
                    <tbody>
                        ${data.tasks.map(task => `
                            <tr>
                                <td>${task.task_id}</td>
                                <td>${task.task_title}</td>
                                <td>${task.requester_name || task.requester_id}</td>
                                <td>${new Date(task.created_at).toLocaleDateString()}</td>
                                <td>${task.objective_title || 'N/A'}</td>
                                <td><button class="view-task-btn" data-task-id="${task.task_id}">Details</button></td>
                            </tr>`).join('')}
                    </tbody>`;
                container.appendChild(table);
                container.querySelectorAll('.view-task-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        showTaskApprovalModal(this.dataset.taskId);
                    });
                });
            } else {
                container.innerHTML = '<p>No tasks pending approval.</p>';
            }
        })
        .catch(handleError);
}

function showTaskApprovalModal(taskId) {
    fetch(`/get_task?task_id=${taskId}`)
        .then(response => response.json())
        .then(task => {
            const detailsContainer = document.getElementById('task-approval-details');
            if (!detailsContainer) return;
            detailsContainer.innerHTML = `
                <h3>${task.task_title}</h3>
                <p><strong>Description:</strong> ${task.task_description}</p>
                <p><strong>Requester:</strong> ${task.requester_id}</p>
                <h4>KPAs:</h4>
                <ul>${task.progress.map(kpa => `<li>${kpa.description.text} (Due: ${new Date(kpa.description.date).toLocaleDateString()})</li>`).join('')}</ul>`;
            document.getElementById('approval-task-id').value = taskId;
            document.getElementById('task-approval-modal').style.display = 'block';
        })
        .catch(handleError);
}

function submitTaskApproval(taskId, decision, comments) {
    fetch('/approve_task', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ task_id: taskId, decision, comments })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) throw new Error(data.error);
        alert(data.message);
        document.getElementById('task-approval-modal').style.display = 'none';
        loadTasksForApproval();
    })
    .catch(handleError);
}

// #endregion

// =================================================================================
// #region VISUALIZATIONS (D3, etc.)
// =================================================================================

// --- Objective Management & D3 Tree ---

// --- These are global or semi-global variables for the D3 tree ---
let i = 0, duration = 750, root;
let svg, treemap; 



/**
 * REVISED: Appends a newly created objective card to the UI.
 * @param {object} objective - The full objective object from the database (must include _id).
 */
function appendNewObjective(objective) {
    const container = document.getElementById('objectives-container');
    if (!container) return;

    const card = document.createElement('div');
    card.className = 'objective-card';
    // Use the MongoDB _id field for the dataset id
    card.dataset.id = objective._id.$oid; 
    
    card.innerHTML = `
        <div class="objective-title">${objective.title}</div>
        <div class="objective-description">${objective.description}</div>
        <div class="objective-weight">Weight: ${objective.weight}</div>
        <button class="delete-btn" data-id="${objective._id.$oid}">Delete</button>
    `;
    container.appendChild(card);
}

function deleteObjective(rowId) {
    fetch(`/delete_objective/${rowId}`, { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'deleted') {
                document.querySelector(`.objective-card[data-id="${rowId}"]`)?.remove();
            }
        })
        .catch(handleError);
}

/**
 * REVISED: Processes a flat list of objectives into a hierarchical structure.
 * This version uses 'title' and 'parent_title' for linking, matching the backend data.
 * @param {Array} objectives - A flat array of objective objects from the database.
 * @returns {object} A root node object suitable for d3.hierarchy.
 */
function processObjectivesData(objectives) {
    const root = { name: "Objectives", children: [] };
    const map = new Map();

    // First, map each objective by its trimmed TITLE and copy all its data.
    objectives.forEach(obj => {
        if (obj.title) {
            const nodeData = { ...obj, children: [] };
            map.set(obj.title.trim(), nodeData);
        }
    });

    // Now, link children to their parents using a trimmed parent_title.
    map.forEach(nodeData => {
        const parentTitle = nodeData.parent_title ? nodeData.parent_title.trim() : '';

        if (parentTitle !== '') {
            const parent = map.get(parentTitle);
            if (parent) {
                // Check to prevent a node from being its own parent
                if (parent.title.trim() !== nodeData.title.trim()) {
                     parent.children.push(nodeData);
                } 
                else if (parent.type=== 'organisational'){

                }
                
                else {
                     console.warn("Circular reference detected: a node's parent is itself.", nodeData);
                     root.children.push(nodeData);
                }
            } else {
                // THIS IS THE FIX: If parent isn't found, log it and treat as a top-level node.
                console.warn(`Parent node with title "${parentTitle}" not found for child "${nodeData.title}". Attaching to root.`);
                root.children.push(nodeData);
            }
        } else {
            // No parent_title means it's a top-level objective.
            root.children.push(nodeData);
        }
    });

    return root;
}




function createCollapsibleTree(data) {
    const container = document.getElementById('tree-container');
    if (!container) {
        console.error("D3 Tree container not found!");
        return;
    }
    container.innerHTML = '';


    // Set the dimensions and margins of the diagram
    var margin = {top: 20, right: 90, bottom: 30, left: 90},
        width = 1060 - margin.right - margin.left,
        height = 950 - margin.top - margin.bottom;

    // Append the svg object to the body of the page
    var svg = d3.select("#tree-container").append("svg")
        .attr("width", width + margin.right + margin.left)
        .attr("height", height + margin.top + margin.bottom)
        .append("g")
        .attr("transform", "translate(" + margin.left + "," + margin.top + ")");

    var i = 0,
        duration = 750,
        root;

    // Declares a tree layout and assigns the size
    var treemap = d3.tree().size([height, width]);

    // Assigns parent, children, height, depth
    root = d3.hierarchy(data, function(d) { return d.children; });
    root.x0 = height / 2;
    root.y0 = 0;

    // Collapse after the second level
    root.children.forEach(collapse);

    // Define the diagonal function for path generation
    function diagonal(s, d) {
        var path = `M ${s.y} ${s.x}
                    C ${(s.y + d.y) / 2} ${s.x},
                      ${(s.y + d.y) / 2} ${d.x},
                      ${d.y} ${d.x}`;
        return path;
    }

    update(root);

    // Collapse the node and all its children
    function collapse(d) {
        if(d.children) {
            d._children = d.children;
            d._children.forEach(collapse);
            d.children = null;
        }
    }



/**
 * REVISED: Creates an HTML input overlay on an SVG text element for editing.
 * Solves the blur/click race condition.
 * @param {object} d - The D3 data object for the node.
 * @param {SVGTextElement} textElement - The SVG text element that was clicked.
 */

function addWeightInput(d, textElement) {
    const textNode = d3.select(textElement);
    const parentNode = d3.select(textElement.parentNode);

    // Prevent adding multiple inputs
    if (parentNode.select('foreignObject').node()) {
        return;
    }

    const currentWeight = d.data.weight || 1.0;
    const textBBox = textElement.getBBox();

    // Hide the original text while editing
    textNode.style('display', 'none');

    const foreignObject = parentNode.append('foreignObject')
        .attr('x', textBBox.x)
        .attr('y', textBBox.y - 10)
        .attr('width', 300)
        .attr('height', 50);

    const div = foreignObject.append('xhtml:div')
        .style('display', 'flex')
        .style('align-items', 'center');

    const input = div.append('xhtml:input')
        .attr('type', 'number')
        .attr('value', currentWeight)
        .style('width', '50px');

    const saveButton = div.append('xhtml:button').text('Save');
    const cancelButton = div.append('xhtml:button').text('Cancel');

    const removeOverlay = () => {
        foreignObject.remove();
        textNode.style('display', null); // Show the text again
    };

    saveButton.on('click', function() {
        d3.event.stopPropagation(); // In v4, use d3.event
        const newWeight = input.node().value;
        saveWeight(d, newWeight);
        // Update tooltip immediately
        textNode.select('title').text(`Weight: ${newWeight}`);
        removeOverlay();
    });

    cancelButton.on('click', function() {
        d3.event.stopPropagation(); // In v4, use d3.event
        removeOverlay();
    });
    
    // *** THE FIX for the race condition ***
    // We listen for 'mousedown' on the overlay. If a click happens
    // outside the overlay, the input will blur. We use a timeout
    // to allow a click on the buttons to register before we remove the overlay.
    let blurTimeout;
    input.on('blur', () => {
        // Set a short timeout to see if a button was clicked.
        // If save/cancel is clicked, the overlay will already be removed
        // and this timeout won't do anything harmful.
        blurTimeout = setTimeout(removeOverlay, 200); 
    });

    input.node().focus();
}



function update(source) {
        // Assigns the x and y position for the nodes
        var treeData = treemap(root);

        // Compute the new tree layout.
        var nodes = treeData.descendants(),
            links = treeData.descendants().slice(1);

        // Normalize for fixed-depth.
        nodes.forEach(function(d){ d.y = d.depth * 280; });

        // ****************** Nodes section ***************************

        // Update the nodes...
        var node = svg.selectAll('g.node')
            .data(nodes, function(d) { return d.id || (d.id = ++i); });

        // Enter any new nodes at the parent's previous position.
        var nodeEnter = node.enter().append('g')
            .attr('class', 'node')
            .attr("transform", function(d) {
                return "translate(" + source.y0 + "," + source.x0 + ")";
            })
            .on('click', function(d) {
                // In D3 v4, the data is the first parameter
                // Check if the click is on the circle (for expanding/collapsing)
                if (d3.event.target.tagName === 'circle') {
                    click(d);
                }
            });

        // Add Circle for the nodes
        nodeEnter.append('circle')
            .attr('class', 'node')
            .attr('r', 1e-6)
            .style("fill", function(d) {
                return d._children ? "lightsteelblue" : "#fff";
            })
            .style('stroke', '#333')
            .style('stroke-width', '1px');

        // Create a group for text elements
        var textGroup = nodeEnter.append('g')
            .attr('class', 'text-group')
            .style('cursor', 'pointer');

        // Add labels for the nodes
        textGroup.append('text')
            .attr("dy", ".35em")
            .attr("x", function(d) {
                return d.children || d._children ? -13 : 13;
            })
            .attr("text-anchor", function(d) {
                return d.children || d._children ? "end" : "start";
            })
            .text(function(d) {
                const title = d.data.name || '';
                const description = d.data.description || '';
                if (description && description.length > 0) {
                    const combinedText = title + ': ' + description;
                    return combinedText.length > 50 ? combinedText.substring(0, 47) + '...' : combinedText;
                } else {
                    return title;
                }
            })
            .on('click', function(d) {
                // In D3 v4, the data is the first parameter
                // Prevent the main node click from firing
                d3.event.stopPropagation();
                console.log("Text clicked:", d);
                addWeightInput(d, this);
            })
            .append("title") // Add tooltip with full text
            .text(function(d) {
                const title = d.data.name || '';
                const description = d.data.description || '';
                const weight = d.data.weight || 1.0;
                return (description ? title + ': ' + description : title) + `\nWeight: ${weight}`;
            });

        // Add weight display below the main text
        textGroup.append('text')
            .attr("dy", "4em")
            .attr("x", function(d) {
                return d.children || d._children ? -13 : 13;
            })
            .attr("text-anchor", function(d) {
                return d.children || d._children ? "end" : "start";
            })
            .attr("class", "weight-text")
            .text(function(d) {
                return `Weight: ${d.data.weight || 1.0}`;
            })
            .style('font-size', '10px')
            .style('fill', '#666');

        // UPDATE
        var nodeUpdate = nodeEnter.merge(node);

        // Transition to the proper position for the node
        nodeUpdate.transition()
            .duration(duration)
            .attr("transform", function(d) {
                return "translate(" + d.y + "," + d.x + ")";
            });

        // Update the node attributes and style
        nodeUpdate.select('circle.node')
            .attr('r', 10)
            .style("fill", function(d) {
                return d._children ? "lightsteelblue" : "#fff";
            })
            .attr('cursor', 'pointer');

        // Update the text group
        nodeUpdate.select('.text-group text:first-child')
            .text(function(d) {
                const title = d.data.name || '';
                const description = d.data.description || '';
                if (description && description.length > 0) {
                    const combinedText = title + ': ' + description;
                    return combinedText.length > 50 ? combinedText.substring(0, 47) + '...' : combinedText;
                } else {
                    return title;
                }
            })
            .on('click', function(d) {
                d3.event.stopPropagation();
                addWeightInput(d, this);
            });

        // Update the weight text
        nodeUpdate.select('.text-group .weight-text')
            .text(function(d) {
                return `Weight: ${d.data.weight || 1.0}`;
            });

        // Remove any exiting nodes
        var nodeExit = node.exit().transition()
            .duration(duration)
            .attr("transform", function(d) {
                return "translate(" + source.y + "," + source.x + ")";
            })
            .remove();

        // On exit reduce the node circles size to 0
        nodeExit.select('circle')
            .attr('r', 1e-6);

        // On exit reduce the opacity of text labels
        nodeExit.select('text')
            .style('fill-opacity', 1e-6);

        // ****************** links section ***************************

        // Update the links...
        var link = svg.selectAll('path.link')
            .data(links, function(d) { return d.id; });

        // Enter any new links at the parent's previous position.
        var linkEnter = link.enter().insert('path', "g")
            .attr("class", "link")
            .attr('d', function(d){
                var o = {x: source.x0, y: source.y0};
                return diagonal(o, o);
            });

        // UPDATE
        var linkUpdate = linkEnter.merge(link);

        // Transition back to the parent element position
        linkUpdate.transition()
            .duration(duration)
            .attr('d', function(d){ return diagonal(d, d.parent) });

        // Remove any exiting links
        link.exit().transition()
            .duration(duration)
            .attr('d', function(d) {
                var o = {x: source.x, y: source.y};
                return diagonal(o, o);
            })
            .remove();

        // Store the old positions for transition.
        nodes.forEach(function(d){
            d.x0 = d.x;
            d.y0 = d.y;
        });
    }

    // Toggle children on click.
    function click(d) {
        if (d.children) {
            d._children = d.children;
            d.children = null;
        } else {
            d.children = d._children;
            d._children = null;
        }
        update(d);
    }
}




/**
 * Draws a radar chart for employee skills using the provided data.
 * This function encapsulates all D3.js logic for creating the chart.
 *
 * @param {object} employeeData - The employee data object containing the .skills array.
 */
function drawSkillRadarChart(employeeData) {
    const chartContainerSelector = '.radar-container';

        // THE FIX: Always clear the container before drawing
    chartContainerSelector.innerHTML = ''; 
    
    // --- 1. Data and Container Validation ---
    const chartContainer = d3.select(chartContainerSelector);
    if (chartContainer.empty()) {
        console.error(`Chart container "${chartContainerSelector}" not found.`);
        return;
    }
    chartContainer.selectAll("*").remove(); // Clear previous chart

    if (!employeeData || !employeeData.skills || employeeData.skills.length === 0) {
        chartContainer.append('p').text('No skill data available to display.');
        return;
    }

    // --- 2. Data Processing and Grouping (The Fix) ---
    // The original code drew a shape for every skill. This is corrected
    // by grouping skills into 'soft' and 'hard' categories first.
    
    // --- THIS IS THE FIX ---
    // Replaced d3.groups() (D3v5+) with d3.nest() (D3v4 compatible)
    const nestedData = d3.nest()
        .key(function(d) { return d.category; })
        .entries(employeeData.skills);

    // d3.nest() returns [{key: "soft", values: [...]}, {key: "hard", values: [...]}].
    // We just need the array of values for the chart.
    const skillGroups = nestedData.map(group => group.values);
    // ----------------------


    // --- 3. D3 Setup and Configuration ---
    const width = 600;
    const height = 600;
    const margin = { top: 100, right: 100, bottom: 100, left: 100 };
    const radius = Math.min(width, height) / 2 - Math.max(...Object.values(margin));
    
    const svg = chartContainer.append('svg')
        .attr('width', width)
        .attr('height', height)
      .append('g')
        .attr('transform', `translate(${width / 2}, ${height / 2})`);

    const levels = 5; //probably should make variable
    const allSkills = employeeData.skills;
    const allAxes = allSkills.map(s => s.skill);
    const totalAxes = allAxes.length;
    const angleSlice = Math.PI * 2 / totalAxes;

    // Define scales
    const rScale = d3.scaleLinear().range([0, radius]).domain([0, 1]);
    const color = d3.scaleOrdinal().domain(['soft', 'hard']).range(['#FFD700', '#FF6347']);

    // --- 4. Draw Grid and Axes ---
    // (The rest of the function remains the same as the bug was only in the grouping logic)
    for (let level = 1; level <= levels; level++) {
        svg.append('circle')
            .attr('class', 'grid-circle')
            .attr('r', radius * (level / levels))
            .style('fill', 'none')
            .style('stroke', '#ccc')
            .style('stroke-dasharray', '2,2');
    }

    const axis = svg.selectAll('.axis')
        .data(allAxes)
        .enter()
        .append('g').attr('class', 'axis');

    axis.append('line')
        .attr('x1', 0).attr('y1', 0)
        .attr('x2', (d, i) => rScale(1.1) * Math.cos(angleSlice * i - Math.PI / 2))
        .attr('y2', (d, i) => rScale(1.1) * Math.sin(angleSlice * i - Math.PI / 2))
        .style('stroke', '#ddd').style('stroke-width', '1px');

    axis.append('text')
        .attr('class', 'legend')
        .style('font-size', '12px').attr('text-anchor', 'middle')
        .attr('dy', '0.35em')
        .attr('x', (d, i) => rScale(1.2) * Math.cos(angleSlice * i - Math.PI / 2))
        .attr('y', (d, i) => rScale(1.2) * Math.sin(angleSlice * i - Math.PI / 2))
        .text(d => d);

    // --- 5. Draw the Data Shapes (Blobs) ---
    const radarLine = d3.lineRadial()
        .curve(d3.curveCardinalClosed)
        .radius(d => rScale(d.count))
        .angle((d, i) => allAxes.indexOf(d.skill) * angleSlice);

    skillGroups.forEach((skills) => {
        const category = skills[0].category;
        svg.append('path')
            .datum(skills)
            .attr('class', 'radar-area')
            .attr('d', radarLine)
            .style('fill', color(category))
            .style('fill-opacity', 0.2);
        svg.append('path')
            .datum(skills)
            .attr('class', 'radar-stroke')
            .attr('d', radarLine)
            .style('stroke', color(category))
            .style('stroke-width', '2px')
            .style('fill', 'none');
    });

    // --- 6. Draw Data Points and Tooltips ---
    svg.selectAll('.dot')
        .data(allSkills)
        .enter()
        .append('circle')
        .attr('class', 'dot')
        .attr('r', 4)
        .attr('cx', (d, i) => rScale(d.count) * Math.cos(angleSlice * i - Math.PI / 2))
        .attr('cy', (d, i) => rScale(d.count) * Math.sin(angleSlice * i - Math.PI / 2))
        .style('fill', d => color(d.category))
    .on('mouseover', function(d) {
        const tooltip = d3.select('body').append('div')
            .attr('class', 'tooltip')
            .style('position', 'absolute')
            .style('background', '#f4f4f4')
            .style('padding', '5px')
            .style('border-radius', '5px')
            .style('pointer-events', 'none');
            
        tooltip.html(`<strong>${d.skill}</strong><br/>Category: ${d.category}<br/>Count: ${d.count}`)
            .style('left', (d3.event.pageX + 10) + 'px')
            .style('top', (d3.event.pageY - 28) + 'px');
        })
        .on('mouseout', function() {
            d3.select('.tooltip').remove();
        });
}





// --- Calendar Heatmap ---

/**
 * Initializes the calendar heatmap component.
 * @param {string} containerId - The ID of the element to render the heatmap in.
 * @param {Array} taskData - The raw data for tasks/sessions.
 * @param {Object} options - Configuration options for the heatmap.
 */
function initFlaskCalendarHeatmap(containerId, taskData, options = {}) {
    // --- Configuration and State ---

    const defaults = {
        colorRange: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127'],
        // UPDATED: This tooltip function is now safer.
        tooltip: (date, hours) => {
            // Check if 'hours' is a valid number; if not, default to 0.
            const displayHours = (typeof hours === 'number') ? hours.toFixed(1) : '0.0';
            console.log(`Tooltip called with date: ${date} of type ${typeof date}, hours: ${hours} of type ${typeof hours}`);
            return `${date.toLocaleDateString()} - ${displayHours} hours`;
        },
        initialPeriod: 'year',
        initialDate: new Date()
    };
    const config = { ...defaults, ...options };

    let currentPeriod = config.initialPeriod;
    let currentDate = new Date(config.initialDate);
    let allProcessedData = []; // Holds aggregated data for each day
    let currentViewData = []; // Holds the data for the currently visible period

    // --- DOM Element Creation ---
    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`Container element with ID ${containerId} not found`);
        return;
    }
    container.innerHTML = ''; // Clear previous content

    // Create and append the header with period controls
    const headerDiv = document.createElement('div');
    headerDiv.className = 'vis-header';
    headerDiv.innerHTML = `
        <h3>Work Duration Heatmap</h3>
        <div id="heatmap-controls" class="vis-controls">
            <button class="control-btn" data-period="year">Year</button>
            <button class="control-btn" data-period="month">Month</button>
            <button class="control-btn" data-period="week">Week</button>
            <button class="control-btn" data-period="day">Day</button>
        </div>
    `;
    container.appendChild(headerDiv);

    // Create the main container for the heatmap visuals
    const heatmapContainer = document.createElement('div');
    heatmapContainer.id = 'calendar-heatmap-container';
    heatmapContainer.style.marginTop = '10px';
    container.appendChild(heatmapContainer);

    // Create and append navigation controls (Previous/Next)
    const navControls = document.createElement('div');
    navControls.id = 'navigation-controls';
    navControls.style.textAlign = 'center';
    navControls.style.marginTop = '1rem';
    navControls.style.display = 'none'; // Initially hidden
    navControls.innerHTML = `
        <button id="prev-btn">Previous</button>
        <span id="nav-title" style="margin: 0 1rem; font-weight: bold;"></span>
        <button id="next-btn">Next</button>
    `;
    container.appendChild(navControls);

    // --- Event Listener Setup ---
    const periodButtons = headerDiv.querySelectorAll('.control-btn');
    const prevBtn = navControls.querySelector('#prev-btn');
    const nextBtn = navControls.querySelector('#next-btn');
    const navTitle = navControls.querySelector('#nav-title');

    periodButtons.forEach(button => {
        button.addEventListener('click', () => {
            periodButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            currentPeriod = button.getAttribute('data-period');
            updateView();
        });
        if (button.getAttribute('data-period') === currentPeriod) {
            button.classList.add('active');
        }
    });

    prevBtn.addEventListener('click', () => navigate(-1));
    nextBtn.addEventListener('click', () => navigate(1));




// This should be placed inside the initFlaskCalendarHeatmap function



/**
 * Processes raw taskData to aggregate totalHours and all task sessions for each day.
 */
function processAllData() {
    const dateValues = {};
    const tasks = Array.isArray(taskData) ? taskData : taskData.heatmap_data || [];

    tasks.forEach(task => {
        if (task.timestamp) { // Use timestamp for more reliable Date creation
            const date = new Date(task.timestamp);
            const dateStr = date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });

            // If this is the first task for the day, create the container object
            if (!dateValues[dateStr]) {
                dateValues[dateStr] = {
                    date: date,
                    dateStr: dateStr,
                    totalHours: 0,
                    sessions: [] // Ensure the sessions array is created
                };
            }
            // Add the hours and push the entire task object into the sessions array
            dateValues[dateStr].totalHours += task.value || 0;
            dateValues[dateStr].sessions.push(task);
            console.log(`Processed task for ${dateStr}:`, task);

        }
    });
    allProcessedData = Object.values(dateValues).sort((a, b) => a.date - b.date);
}




/**
 * Filters the processed data based on the current period (Year, Month, etc.).
 * This function sets the `currentViewData` variable.
 */
function filterDataForView() {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    if (currentPeriod === 'day') {
        const dayStr = currentDate.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
        // **FIX APPLIED HERE**: Search the master list for day data.
        const dayData = allProcessedData.find(d => d.dateStr === dayStr);
        currentViewData = dayData ? dayData.sessions : [];

    } else if (currentPeriod === 'year') {
        currentViewData = allProcessedData.filter(day => day.date.getFullYear() === year);

    } else if (currentPeriod === 'month') {
        currentViewData = allProcessedData.filter(day =>
            day.date.getFullYear() === year && day.date.getMonth() === month
        );
        
    } else if (currentPeriod === 'week') {
        const weekStart = new Date(currentDate);
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
        weekStart.setHours(0, 0, 0, 0); // Normalize to start of the day

        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekEnd.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999); // Normalize to end of the day
        
        currentViewData = allProcessedData.filter(day =>
            day.date >= weekStart && day.date <= weekEnd
        );
    }
}


// This should be placed inside the initFlaskCalendarHeatmap function

/**
 * The main function to orchestrate a view update. It filters data and calls a renderer.
 */
function updateView() {
    filterDataForView();
    updateNavTitle();
    navControls.style.display = (currentPeriod !== 'year') ? 'flex' : 'none';

    switch (currentPeriod) {
        case 'year': renderYearView(); break;
        case 'month': renderMonthView(); break;
        case 'week': renderWeekView(); break; // To be implemented
        case 'day': renderDayView(); break;
    }
}

/**
 * Handles date changes for the Previous/Next buttons.
 * @param {number} direction - -1 for previous, 1 for next.
 */
function navigate(direction) {
    switch (currentPeriod) {
        case 'year': currentDate.setFullYear(currentDate.getFullYear() + direction); break;
        case 'month': currentDate.setMonth(currentDate.getMonth() + direction); break;
        case 'week': currentDate.setDate(currentDate.getDate() + direction * 7); break;
        case 'day': currentDate.setDate(currentDate.getDate() + direction); break;
    }
    updateView();
}

/**
 * Updates the title displayed between the navigation buttons (e.g., "July 2025").
 */
function updateNavTitle() {
    let title = '';
    if (currentPeriod === 'year') {
        title = currentDate.getFullYear().toString();
    } else if (currentPeriod === 'month') {
        title = currentDate.toLocaleString('default', { month: 'long', year: 'numeric' });
    } else if (currentPeriod === 'day') {
        title = currentDate.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
    } else if (currentPeriod === 'week') {
        const weekStart = new Date(currentDate);
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekEnd.getDate() + 6);
        const options = { month: 'short', day: 'numeric' };
        title = `${weekStart.toLocaleDateString('en-US', options)} - ${weekEnd.toLocaleDateString('en-US', options)}, ${weekEnd.getFullYear()}`;
    }
    navTitle.textContent = title;
}

// --- UTILITY RENDER FUNCTIONS ---

/**
 * Calculates the appropriate color from the color range based on the number of hours.
 * @param {number} hours - The number of hours worked.
 * @param {number} maxHours - The maximum hours in the current view for normalization.
 * @returns {string} - The hex color code.
 */
function getColorForHours(hours, maxHours) {
    if (hours <= 0) return config.colorRange[0];
    const normalized = Math.min(hours / maxHours, 1);
    // Use Math.ceil to ensure that even small values get the first color level
    const colorIndex = Math.max(1, Math.ceil(normalized * (config.colorRange.length - 1)));
    return config.colorRange[colorIndex];
}


// function createTooltip(parentContainer) {
//     let tooltip = parentContainer.querySelector('.heatmap-tooltip');
//     if (!tooltip) {
//         tooltip = document.createElement('div');
//         tooltip.className = 'heatmap-tooltip';
//         tooltip.style.position = 'absolute'; // Use absolute for positioning relative to the page
//         tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';
//         tooltip.style.color = '#fff';
//         tooltip.style.padding = '8px 12px';
//         tooltip.style.borderRadius = '4px';
//         tooltip.style.fontSize = '12px';
//         tooltip.style.maxWidth = '250px'; // Prevent it from getting too wide
//         tooltip.style.pointerEvents = 'none';
//         tooltip.style.opacity = '0';
//         tooltip.style.transition = 'opacity 0.2s, top 0.2s, left 0.2s';
//         tooltip.style.zIndex = '1000';
//         tooltip.style.whiteSpace = 'normal';
//         document.body.appendChild(tooltip); // Append to body to avoid clipping issues
//     }

//     return {
//         show: (x, y, dayData) => {
//             let content = 'No activity recorded.';
//             // If we have data for the day, build the detailed content string
//             if (dayData && dayData.sessions && dayData.sessions.length > 0) {
//                 const titles = [...new Set(dayData.sessions.map(s => s.task_title))].join(', ');
//                 const totalHours = dayData.totalHours.toFixed(1);

//                 content = `<strong>${dayData.date.toLocaleDateString('en-US', {weekday: 'long', month: 'long', day: 'numeric'})}</strong><br>` +
//                           `Worked on <strong>${titles}</strong> for approx. <strong>${totalHours} hours</strong>.`;

//                 // Add descriptions if they exist
//                 const descriptions = dayData.sessions.map(s => s.description).filter(d => d && d !== 'No description');
//                 if (descriptions.length > 0) {
//                     content += "<ul style='margin: 5px 0 0 15px; padding: 0;'>";
//                     descriptions.forEach(desc => {
//                         content += `<li>${desc}</li>`;
//                     });
//                     content += "</ul>";
//                 }
//             }
//             // Use innerHTML since we are adding HTML tags like <strong> and <ul>
//             tooltip.innerHTML = content;
//             tooltip.style.opacity = '1';

//             // More robust positioning to avoid going off-screen
//             const { width: tipWidth, height: tipHeight } = tooltip.getBoundingClientRect();
//             let newX = x + 15;
//             let newY = y - tipHeight - 10; // Default position is above the cursor

//             // Adjust if it goes off the right edge
//             if (newX + tipWidth > window.innerWidth) {
//                 newX = window.innerWidth - tipWidth - 10;
//             }
//             // Adjust if it goes off the top edge
//             if (newY < 0) {
//                 newY = y + 15;
//             }

//             tooltip.style.left = `${newX}px`;
//             tooltip.style.top = `${newY}px`;
//         },
//         hide: () => {
//             tooltip.style.opacity = '0';
//         }
//     };
// }


// --- VIEW-SPECIFIC RENDERERS ---


/**
 * Creates and manages a detailed tooltip.
 * @param {HTMLElement} parentContainer - The element to append the tooltip to.
 * @returns {Object} - An object with show and hide methods.
 */
function createTooltip(parentContainer) {
    let tooltip = document.body.querySelector('.heatmap-tooltip');
    if (!tooltip) {
        tooltip = document.createElement('div');
        tooltip.className = 'heatmap-tooltip';
        tooltip.style.position = 'absolute';
        tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';
        tooltip.style.color = '#fff';
        tooltip.style.padding = '8px 12px';
        tooltip.style.borderRadius = '4px';
        tooltip.style.fontSize = '12px';
        tooltip.style.maxWidth = '250px';
        tooltip.style.pointerEvents = 'none';
        tooltip.style.opacity = '0';
        tooltip.style.transition = 'opacity 0.2s';
        tooltip.style.zIndex = '1000';
        document.body.appendChild(tooltip); // Append to body to avoid clipping
    }

    return {
        // show: (x, y, dayData) => {
        //     let content;
        //     console.log('dayData', dayData);
        //     // Check if we have data for the hovered day
        //     if (dayData && dayData.sessions.length > 0) {
        //         const totalHours = dayData.totalHours.toFixed(1);
        //         content = `<strong>${dayData.date.toLocaleDateString()}</strong><br>Worked approx. <strong>${totalHours} hours</strong>.`;
                
        //         // Create a list of descriptions
        //         const descriptions = dayData.sessions.map(s => `<li>${s.task_title}: ${s.description}</li>`).join('');
        //         if (descriptions) {
        //             content += `<ul style='margin: 5px 0 0 15px; padding: 0;'>${descriptions}</ul>`;
        //         }
        //     } else {
        //         content = 'No activity recorded.';
        //     }
            
        //     tooltip.innerHTML = content;
        //     tooltip.style.opacity = '1';
        //     tooltip.style.left = `${x + 15}px`;
        //     tooltip.style.top = `${y + 15}px`;
        // },
      
      
// Improved createTooltip function - replace the show method with this:



show: (x, y, dayData) => {
    let content;
    console.log('dayData', dayData);
    
    // Check if we have data for the hovered day and if sessions exists and has content
    if (dayData && dayData.sessions && dayData.sessions.length > 0) {
        const totalHours = dayData.totalHours.toFixed(1);
        content = `<strong>${dayData.date.toLocaleDateString()}</strong><br>Worked approx. <strong>${totalHours} hours</strong>.`;
        
        // Create a list of descriptions
        const descriptions = dayData.sessions.map(s => `<li>${s.task_title}: ${s.description}</li>`).join('');
        if (descriptions) {
            content += `<ul style='margin: 5px 0 0 15px; padding: 0;'>${descriptions}</ul>`;
        }
    } else {
        // Handle the case where there's no data or no sessions
        const dateStr = dayData && dayData.date ? dayData.date.toLocaleDateString() : 'Unknown date';
        content = `<strong>${dateStr}</strong><br>No activity recorded.`;
    }
    
    tooltip.innerHTML = content;
    tooltip.style.opacity = '1';
    tooltip.style.left = `${x + 15}px`;
    tooltip.style.top = `${y + 15}px`;
},
      
      
      
      
      
        hide: () => {
            tooltip.style.opacity = '0';
        }
    };
}







/**
 * Renders the full year heatmap.
 */
function renderYearView() {
    heatmapContainer.innerHTML = '';
    const year = currentDate.getFullYear();
    const tooltip = createTooltip(heatmapContainer);

    const svg = d3.select(heatmapContainer).append("svg")
        .attr("width", "100%")
        .attr("height", "auto")
        .attr("viewBox", "0 -25 722 110")
        .style("overflow", "visible");

    const cellSize = 13;
    const yearData = d3.timeDays(new Date(year, 0, 1), new Date(year + 1, 0, 1));
    const dataMap = new Map(currentViewData.map(d => [d.dateStr, d.totalHours]));
    console.log('looking at the datamap', dataMap)
    const maxHours = Math.max(...dataMap.values(), 1);

    svg.append("g")
        .selectAll("rect")
        .data(yearData)
        .enter().append("rect")
        .attr("width", cellSize - 1.5)
        .attr("height", cellSize - 1.5)
        .attr("x", d => d3.timeWeek.count(d3.timeYear(d), d) * cellSize + 10)
        .attr("y", d => d.getDay() * cellSize + 10)
        .attr("fill", d => {
            const dateStr = d.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
            return getColorForHours(dataMap.get(dateStr) || 0, maxHours);
        })


// USE THIS EXACT BLOCK IN renderYearView and renderMonthView
// .on("mouseover", function(d) { // 'd' is the Date object for the cell
//     const dateStr = d.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
//     const dayData = dataMap.get(dateStr); // Get the full object from the map
    
//     // Pass the entire dayData object (or undefined if no activity) to the tooltip
//     tooltip.show(d3.event.pageX, d3.event.pageY, dayData);
// })
// .on("mouseout", tooltip.hide);
 

// Fixed mouse event handler for renderYearView() - D3 v4 version
// Replace the existing mouseover/mouseout block with this:
.on("mouseover", function(d) { // D3 v4 passes data as first parameter
    const dateStr = d.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
    
    // Find the complete day data from currentViewData instead of just the hours
    const dayData = currentViewData.find(day => day.dateStr === dateStr);
    
    // Create a consistent data structure for the tooltip
    const tooltipData = dayData || {
        date: d,
        dateStr: dateStr,
        totalHours: 0,
        sessions: []
    };
    
    tooltip.show(d3.event.pageX, d3.event.pageY, tooltipData);
})
.on("mouseout", tooltip.hide);




    // Add month labels
    svg.append("g")
        .selectAll("text")
        .data(d3.range(12))
        .enter().append("text")
        .attr("x", d => d3.timeWeek.count(new Date(year, 0, 1), new Date(year, d, 1)) * cellSize + 20)
        .attr("y", 0)
        .text(d => d3.timeFormat("%b")(new Date(year, d, 1)));
}


/**
 * Renders a compact, HORIZONTAL heatmap for a single month.
 */
function renderMonthView() {
    heatmapContainer.innerHTML = '';
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const tooltip = createTooltip(heatmapContainer);

    const cellSize = 6;

    // ADJUSTED: The viewBox is now wider than it is tall for the horizontal layout.
    const svg = d3.select(heatmapContainer).append("svg")
        .attr("width", "100%")
        .attr("height", "80%")
        .attr("viewBox", `-10 5 185 50`)
        .style("overflow", "visible");

    const monthData = d3.timeDays(new Date(year, month, 1), new Date(year, month + 1, 0));
    const dataMap = new Map(currentViewData.map(d => [d.dateStr, d.totalHours]));
    console.log('looking at the month datamap', dataMap)
    const maxHours = Math.max(...dataMap.values(), 1);
    
    const firstDayOfMonth = new Date(year, month, 1).getDay();

    // ADJUSTED: The group's translation provides padding for the new orientation.
    const monthGroup = svg.append("g")
        .attr("transform", "translate(15, 20)");

    // ADJUSTED: Day of the week labels (S, M, T...) are now positioned at the top.
    const dayLabels = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
    monthGroup.selectAll("text.day-label")
        .data(dayLabels)
        .enter().append("text")
        .attr("x", (d, i) => i * cellSize + cellSize / 2) // Horizontal position based on index
        .attr("y", -8) // Positioned above the grid
        .attr("text-anchor", "middle")
        .attr("font-size", "4px")
        .attr("fill", "#666")
        .text(d => d);

    // The rectangles for each day
    monthGroup.selectAll("rect")
        .data(monthData)
        .enter().append("rect")
        .attr("width", cellSize - 1.5)
        .attr("height", cellSize - 1.5)
        // SWAPPED: 'x' is now the day of the week (column).
        .attr("x", d => d.getDay() * cellSize)
        // SWAPPED: 'y' is now the week of the month (row).
        .attr("y", d => Math.floor((d.getDate() - 1 + firstDayOfMonth) / 7) * cellSize)
        .attr("fill", d => {
            const dateStr = d.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
            return getColorForHours(dataMap.get(dateStr) || 0, maxHours);
        })
        .attr("rx", 3)

// USE THIS EXACT BLOCK IN renderYearView and renderMonthView
// .on("mouseover", function(d) { // 'd' is the Date object for the cell
//     const dateStr = d.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
//     const dayData = dataMap.get(dateStr); // Get the full object from the map
    
//     // Pass the entire dayData object (or undefined if no activity) to the tooltip
//     tooltip.show(d3.event.pageX, d3.event.pageY, dayData);
// })
// .on("mouseout", tooltip.hide);

.on("mouseover", function(d) { // D3 v4 passes data as first parameter
    const dateStr = d.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
    
    // Find the complete day data from currentViewData instead of just the hours
    const dayData = currentViewData.find(day => day.dateStr === dateStr);
    
    // Create a consistent data structure for the tooltip
    const tooltipData = dayData || {
        date: d,
        dateStr: dateStr,
        totalHours: 0,
        sessions: []
    };
    
    tooltip.show(d3.event.pageX, d3.event.pageY, tooltipData);
})
.on("mouseout", tooltip.hide);



            // ADDED: This block adds the date numbers inside each square.
    monthGroup.selectAll("text.date")
        .data(monthData)
        .enter().append("text")
        .attr("class", "date")
        // Position text in the horizontal center of the cell
        .attr("x", d => (d.getDay() * cellSize) + (cellSize / 2)-1)
        // Position text in the vertical center of the cell, with a small adjustment for the baseline
        .attr("y", d => (Math.floor((d.getDate() - 1 + firstDayOfMonth) / 7) * cellSize) + (cellSize / 2) + 0.4)
        .attr("text-anchor", "middle")
        .attr("font-size", "3px") // Set the requested font size
        .attr("fill", "#000")
        .style("pointer-events", "none") // Makes the text non-interactive
        .text(d => d.getDate());
}




/**
 * Renders the view for a single week.
 */
function renderWeekView() {
    heatmapContainer.innerHTML = '';
    const tooltip = createTooltip(heatmapContainer);
    const dataMap = new Map(currentViewData.map(d => [d.dateStr, d]));
    const maxHours = Math.max(...Array.from(dataMap.values()).map(d => d.totalHours), 1);

    const weekGrid = document.createElement('div');
    weekGrid.style.display = 'grid';
    weekGrid.style.gridTemplateColumns = 'repeat(7, 1fr)';
    weekGrid.style.gap = '5px';

    const weekStart = new Date(currentDate);
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());

    for (let i = 0; i < 7; i++) {
        const day = new Date(weekStart);
        day.setDate(day.getDate() + i);
        const dayStr = day.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
        const dayData = dataMap.get(dayStr);
        const hours = dayData ? dayData.totalHours : 0;

        const dayCell = document.createElement('div');
        dayCell.style.padding = '10px';
        dayCell.style.textAlign = 'center';
        dayCell.style.backgroundColor = getColorForHours(hours, maxHours);
        dayCell.style.color = hours > maxHours / 2 ? 'white' : 'black';
        dayCell.style.borderRadius = '3px';
        dayCell.innerHTML = `
            <strong>${day.toLocaleDateString('en-US', { weekday: 'short' })}</strong>
            <div>${day.getDate()}</div>
        `;
        
        dayCell.onmouseover = (event) => {
            tooltip.show(event.pageX, event.pageY, config.tooltip(day, hours));
        };
        dayCell.onmouseout = tooltip.hide;
        
        weekGrid.appendChild(dayCell);
    }
    heatmapContainer.appendChild(weekGrid);
}


/**
 * Renders the timeline view for a single day.
 */
function renderDayView() {
    heatmapContainer.innerHTML = '';

    if (currentViewData.length === 0) {
        heatmapContainer.innerHTML = '<p style="text-align:center; padding: 2rem;">No work sessions recorded for this day.</p>';
        return;
    }

    const dayContainer = document.createElement('div');
    dayContainer.style.position = 'relative';
    dayContainer.style.height = '480px'; // 20px per hour
    dayContainer.style.borderLeft = '2px solid #e0e0e0';
    dayContainer.style.marginLeft = '50px'; // Space for time labels
    dayContainer.style.marginTop = '20px';
    
    // Add time labels (00:00, 01:00, etc.)
    for (let hour = 0; hour <= 24; hour++) {
        const timeLabel = document.createElement('div');
        timeLabel.textContent = `${String(hour).padStart(2, '0')}:00`;
        timeLabel.style.position = 'absolute';
        timeLabel.style.left = '-55px';
        timeLabel.style.top = `${(hour / 24) * 100}%`;
        timeLabel.style.transform = 'translateY(-50%)';
        timeLabel.style.fontSize = '10px';
        timeLabel.style.color = '#666';
        dayContainer.appendChild(timeLabel);
    }
    
    const maxHoursInSession = Math.max(...currentViewData.map(s => s.value), 1);

    // Sort sessions by time to handle overlaps visually
    const sortedSessions = currentViewData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    
    sortedSessions.forEach((session, index) => {
        const startTime = new Date(session.timestamp);
        const startHour = startTime.getHours() + (startTime.getMinutes() / 60);
        const durationHours = session.value;

        const startPercent = (startHour / 24) * 100;
        const heightPercent = (durationHours / 24) * 100;

        const sessionBlock = document.createElement('div');
        sessionBlock.style.position = 'absolute';
        sessionBlock.style.left = '0px'; 
        sessionBlock.style.width = `90%`; // Allow for some spacing
        sessionBlock.style.top = `${startPercent}%`;
        sessionBlock.style.height = `${heightPercent}%`;
        sessionBlock.style.backgroundColor = getColorForHours(durationHours, maxHoursInSession);
        sessionBlock.style.borderRadius = '3px';
        sessionBlock.style.overflow = 'hidden';
        sessionBlock.style.padding = '2px 4px';
        sessionBlock.style.fontSize = '12px';
        sessionBlock.style.color = durationHours > maxHoursInSession / 2 ? 'white' : 'black';
        sessionBlock.innerHTML = `<strong>${session.task_title}</strong> (${durationHours.toFixed(1)} hrs)`;
        sessionBlock.title = `${session.task_title}\n${session.description}`;

    // Add the mouseover event directly here
    sessionBlock.addEventListener('mouseover', (event) => {
        // Here, 'session' is the data object itself.
        // We wrap it in a structure that the tooltip function expects.
        const tooltipData = {
            date: new Date(session.timestamp),
            totalHours: session.value,
            sessions: [session] // Pass the session as an array
        };
        tooltip.show(event.pageX, event.pageY, tooltipData);
    });
    
    sessionBlock.addEventListener('mouseout', () => {
        tooltip.hide();
    });
    dayContainer.appendChild(sessionBlock);
        });

    heatmapContainer.appendChild(dayContainer);
}



    // --- Initial Load ---
    processAllData();
    updateView();

}






function generateHeatmapData(flaskData) {
    const groupedData = (flaskData.heatmap_data || []).reduce((acc, entry) => {
        const dateKey = entry.date.split('T')[0];
        if (!acc[dateKey]) {
            acc[dateKey] = { date: new Date(dateKey), details: [], summary: [] };
        }
        acc[dateKey].details.push({ name: entry.task_title, value: entry.value, description: entry.description });
        acc[dateKey].summary = acc[dateKey].details;
        return acc;
    }, {});

    return Object.values(groupedData).map(entry => ({...entry, init: function() { this.total = this.details.reduce((a, d) => a + d.value, 0); return this; }}).init());
}

// #endregion


// =================================================================================
// #region OBJECTIVE WEIGHT MANAGEMENT (NEW)
// =================================================================================

function saveWeight(d, newWeight) {
    if (!d.data || !d.data.id) {
        const errorMsg = "Cannot save weight: The selected objective is missing its database ID. This might be the root node, which cannot be edited.";
        console.error(errorMsg, d);
        handleError(new Error(errorMsg));
        return;
    }
    
    const objectiveId = d.data.id; // Use the simple 'id' field.
    const weightValue = parseFloat(newWeight);

    if (isNaN(weightValue)) {
        handleError(new Error("Weight must be a valid number."));
        return;
    }
    
    d.data.weight = weightValue;

    fetch('/objectives/update_weight', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: objectiveId, weight: weightValue })
    })
    .then(handleResponse)
    .catch(error => {
        handleError(error);
        d.data.weight = d.data.weight;
    });
}





// =================================================================================
// #region GAMIFICATION (NEW)
// =================================================================================

function initializeGamificationSettings() {
    const form = document.getElementById('rank-settings-form');
    if (!form) return;

    loadRankSettings();

    document.getElementById('add-rank-btn').addEventListener('click', () => {
        addRankInputGroup();
    });

    form.addEventListener('submit', handleSaveRankSettings);
}

function loadRankSettings() {
    fetch('/gamification/settings')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('rank-inputs-container');
            container.innerHTML = ''; // Clear existing
            data.ranks.forEach(rank => {
                addRankInputGroup(rank.name, rank.points);
            });
        })
        .catch(handleError);
}

function addRankInputGroup(name = '', points = 0) {
    const container = document.getElementById('rank-inputs-container');
    const group = document.createElement('div');
    group.className = 'rank-input-group';
    group.innerHTML = `
        <input type="text" class="rank-name" placeholder="Rank Name" value="${name}" required>
        <input type="number" class="rank-points" placeholder="Points Required" value="${points}" required>
        <button type="button" class="delete-rank-btn">&times;</button>
    `;
    container.appendChild(group);
    group.querySelector('.delete-rank-btn').addEventListener('click', () => {
        group.remove();
    });
}

function handleSaveRankSettings(event) {
    event.preventDefault();
    const statusEl = document.getElementById('rank-settings-status');
    const ranks = [];
    document.querySelectorAll('.rank-input-group').forEach(group => {
        const name = group.querySelector('.rank-name').value;
        const points = parseInt(group.querySelector('.rank-points').value, 10);
        if (name && !isNaN(points)) {
            ranks.push({ name, points });
        }
    });

    const settings = { ranks: ranks };

    fetch('/gamification/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
    })
    .then(handleResponse)
    .then(data => {
        statusEl.textContent = 'Settings saved successfully!';
        statusEl.style.color = 'lime';
        setTimeout(() => statusEl.textContent = '', 3000);
    })
    .catch(error => {
        statusEl.textContent = `Error: ${error.message}`;
        statusEl.style.color = 'red';
        handleError(error);
    });
}





/**
 * Renders the gamification visualizations based on employee data.
 * @param {object} employeeData - The full data object for the employee.
 */

function renderGamificationVisuals(employeeData) {
    const mainContainer = document.getElementById('gamification-visual');
    if (!mainContainer) return;
    mainContainer.innerHTML = '';
    
    const gamificationData = employeeData.gamification;
    if (!gamificationData) {
        mainContainer.innerHTML = '<p>Gamification data not available.</p>';
        return;
    }

    const toggleButton = document.createElement('button');
    toggleButton.textContent = 'Switch to 3D Cone View';
    toggleButton.style.marginBottom = '10px';
    mainContainer.appendChild(toggleButton);
    const visHolder = document.createElement('div');
    visHolder.id = 'gamification-visual-holder';
    mainContainer.appendChild(visHolder);
    let isConeView = false;
    const showDonut = () => {
        visHolder.innerHTML = '';
        createDonutLoader(visHolder, employeeData); // Pass full employee data for name
        toggleButton.textContent = 'Switch to 3D Cone View';
        isConeView = false;
    };
    const showCone = () => {
        visHolder.innerHTML = '';
        create3DRankCone(visHolder, employeeData);
        toggleButton.textContent = 'Switch to Donut View';
        isConeView = true;
    };
    toggleButton.addEventListener('click', () => { isConeView ? showDonut() : showCone(); });
    showDonut();
}




/**
 * REWRITTEN: Creates the jewel fragment chart.
 * @param {HTMLElement} container - The DOM element to append the chart to.
 * @param {object} employeeData - The full data object for the employee.
 */
function createDonutLoader(container, employeeData) {
    const gamificationData = employeeData.gamification;
    const employeeName = employeeData.first_name || 'Employee';

    // Main wrapper for all visual elements
    const chartContainer = document.createElement('div');
    chartContainer.className = 'chart-container';

    // -- Info Panel --
    const rankInfo = document.createElement('div');
    rankInfo.className = 'rank-info';
    
    const progressFill = document.createElement('div');
    progressFill.className = 'progress-fill';
    
    const rankList = document.createElement('div');
    rankList.className = 'rank-list';
    
    gamificationData.all_ranks.forEach(rank => {
        const rankItem = document.createElement('div');
        rankItem.className = 'rank-item';
        rankItem.textContent = `${rank.name} (${rank.points / 1000}k)`;
        if (gamificationData.current_rank.name === rank.name) {
            rankItem.classList.add('current');
        }
        if (gamificationData.total_points >= rank.points) {
            rankItem.classList.add('achieved');
        }
        rankList.appendChild(rankItem);
    });

    rankInfo.innerHTML = `
        <h3>${employeeName.toUpperCase()}'S PROGRESS</h3>
        <div class="current-rank">${gamificationData.current_rank.name}</div>
        <div style="font-size: 12px; color: #999;">Current: ${gamificationData.current_rank.points.toLocaleString()} pts</div>
        <div style="font-size: 12px; color: #999;">Next: ${gamificationData.next_rank ? gamificationData.next_rank.points.toLocaleString() + ' pts' : 'Max'}</div>
        <div class="progress-bar"></div>
        <div class="progress-text" style="font-size: 11px; text-align: center; color: #999;">
            ${gamificationData.is_at_highest_rank ? 'Max Rank Achieved' : `${Math.round(gamificationData.progress_percentage)}% to ${gamificationData.next_rank.name}`}
        </div>
    `;
    rankInfo.querySelector('.progress-bar').appendChild(progressFill);
    rankInfo.appendChild(rankList);
    
    setTimeout(() => {
        progressFill.style.width = `${gamificationData.progress_percentage}%`;
    }, 500);

    // -- Donut Chart area --
    const donutChart = document.createElement('div');
    donutChart.className = 'donut-chart';
    
    const orbitRing = document.createElement('div');
    orbitRing.className = 'orbit-ring';

    const centerGem = document.createElement('div');
    centerGem.className = 'center-gem';
    centerGem.innerHTML = `
        <svg width="80" height="80" viewBox="0 0 80 80">
            <polygon points="40,8 65,25 60,55 20,55 15,25" />
            <polygon points="40,18 55,30 50,45 30,45 25,30" fill="rgba(255,255,255,0.2)" />
        </svg>
    `;
    
    const svgNS = "http://www.w3.org/2000/svg";
    const svg = document.createElementNS(svgNS, "svg");
    svg.setAttribute('width', '400');
    svg.setAttribute('height', '400');
    svg.setAttribute('viewBox', '0 0 400 400');
    svg.style.position = 'absolute';
    
    const totalFragments = 18;
    const fragmentsToLight = Math.floor((gamificationData.progress_percentage / 100) * totalFragments);
    
    for (let i = 0; i < totalFragments; i++) {
        const angle = i * (360 / totalFragments);
        const g = document.createElementNS(svgNS, 'g');
        g.classList.add('jewel-fragment', 'progress-fragment');
        if (i < fragmentsToLight) {
            g.classList.add('lit');
        }
        g.style.transform = `translate(200px, 200px) rotate(${angle}deg) translate(160px) rotate(-${angle}deg)`;
        
        const fragmentSvg = document.createElementNS(svgNS, 'svg');
        const polygon = document.createElementNS(svgNS, 'polygon');
        polygon.setAttribute('points', '12,2 20,8 18,18 6,18 4,8');
        fragmentSvg.appendChild(polygon);
        g.appendChild(fragmentSvg);
        svg.appendChild(g);
    }
    
    donutChart.appendChild(orbitRing);
    donutChart.appendChild(svg);
    donutChart.appendChild(centerGem);
    
    // -- Background effects --
    const glowEffect = document.createElement('div');
    glowEffect.className = 'glow-effect';
    
    const particles = document.createElement('div');
    particles.className = 'floating-particles';

    // Assemble the final structure
    chartContainer.appendChild(glowEffect);
    chartContainer.appendChild(rankInfo);
    chartContainer.appendChild(donutChart);
    chartContainer.appendChild(particles);

    // Append the fully constructed chart to the main container
    container.appendChild(chartContainer);
}




/**
 * REWRITTEN: Creates the 3D rank cone visualization.
 * @param {HTMLElement} container - The DOM element to render the chart in.
 * @param {object} employeeData - The full data object for the employee.
 */
function create3DRankCone(container, employeeData) {
    if (typeof THREE === 'undefined') {
        handleError(new Error("Three.js has not loaded correctly."));
        return;
    }
    const gamificationData = employeeData.gamification;

    // --- Create Layout ---
    const wrapper = document.createElement('div');
    wrapper.className = 'chart-container'; // Reuse the main wrapper class
    
    const canvasContainer = document.createElement('div');
    canvasContainer.className = 'cone-canvas-container'; // For the 3D canvas
    
    const legendContainer = document.createElement('div');
    legendContainer.className = 'rank-info'; // Reuse legend class

    // --- Populate Legend ---
    console.log('checking gamification data: ',gamificationData.all_ranks)
    const sortedRanks = [...gamificationData.all_ranks].sort((a, b) => a.points - b.points);
    const rankList = document.createElement('div');
    rankList.className = 'rank-list';

    sortedRanks.forEach(rank => {
        const rankItem = document.createElement('div');
        rankItem.className = 'rank-item';
        rankItem.textContent = `${rank.name} (${rank.points / 1000}k)`;
        if (gamificationData.current_rank.name === rank.name) rankItem.classList.add('current');
        if (gamificationData.total_points >= rank.points) rankItem.classList.add('achieved');
        rankList.appendChild(rankItem);
    });
    legendContainer.innerHTML = `<h3>Rank Progression</h3>`;
    legendContainer.appendChild(rankList);

    // Assemble layout
    wrapper.appendChild(legendContainer);
    wrapper.appendChild(canvasContainer);
    container.appendChild(wrapper);

    // --- Three.js Logic ---
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x0a0a1a);
    const camera = new THREE.PerspectiveCamera(75, canvasContainer.clientWidth / canvasContainer.clientHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(canvasContainer.clientWidth, canvasContainer.clientHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    canvasContainer.appendChild(renderer.domElement);

    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);
    const pointLight = new THREE.PointLight(0xffffff, 0.8, 100);
    pointLight.position.set(0, 10, 10);
    scene.add(pointLight);

    const coneGroup = new THREE.Group();
    scene.add(coneGroup);
    
    const rankColors = [0x8B4513, 0xA0522D, 0xCD853F, 0xCD7F32, 0xB87333, 0xDAA520, 0xC0C0C0, 0xD3D3D3, 0xE6E6FA, 0xFFD700];

    const coneHeight = 10;
    const maxRadius = 4;
    const sliceHeight = coneHeight / sortedRanks.length;

    sortedRanks.forEach((rank, i) => {
        const bottomRadius = maxRadius * (i / sortedRanks.length);
        const topRadius = maxRadius * ((i + 1) / sortedRanks.length);

        const geometry = new THREE.CylinderGeometry(topRadius, bottomRadius, sliceHeight, 64, 1, true);
        const material = new THREE.MeshStandardMaterial({
            color: rankColors[i % rankColors.length],
            side: THREE.DoubleSide,
            metalness: 0.6,
            roughness: 0.3,
            transparent: true,
            opacity: employeeData.gamification.total_points >= rank.points ? 0.9 : 0.25
        });

        const slice = new THREE.Mesh(geometry, material);
        slice.position.y = (i * sliceHeight) - (coneHeight / 2) + (sliceHeight / 2);
        coneGroup.add(slice);
        
        // Add a highlight glow for the current rank
        if (rank.name === employeeData.gamification.current_rank.name) {
            const glowGeometry = new THREE.CylinderGeometry(topRadius + 0.1, bottomRadius + 0.1, sliceHeight, 64, 1, true);
            const glowMaterial = new THREE.MeshBasicMaterial({
                color: 0xffffff,
                side: THREE.BackSide,
                transparent: true,
                opacity: 0.5
            });
            const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
            slice.add(glowMesh);
        }
    });

    camera.position.set(0, 0, 12);
    camera.lookAt(0, 0, 0);

    const controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    
    function animate() {
        requestAnimationFrame(animate);
        controls.update();
        coneGroup.rotation.y += 0.005;
        renderer.render(scene, camera);
    }
    animate();
}



// #endregion






// =================================================================================
// #region HELPERS & UTILITIES
// =================================================================================

function calculateProgressWidth(progress) {
    if (!progress?.length) return 0;
    const approved = progress.filter(p => p.status === 'Approved').length;
    return Math.round((approved / progress.length) * 100);
}

function getProgressClass(status) {
    const classMap = {
        'Approved': 'completed',
        'Revisions Requested': 'revisions-needed',
        'Under Review': 'under-review',
        'Not Started': 'not-started'
    };
    return classMap[status] || 'in-progress';
}

function handleResponse(response) {
    return response.json().then(data => {
        if (!response.ok) {
            throw new Error(data.error || 'Network response was not ok');
        }
        if (data.ranking) {
            displayRecommendations(data.ranking);
        }
        return data;
    });
}

/**
 * Tears down the Employee Overview visualizations to prevent state conflicts.
 */
function teardownEmployeeOverview() {
    console.log("Tearing down Employee Overview visualizations.");
    const radarContainer = document.getElementById("skillsRadarChart");
    const calendarContainer = document.getElementById("calendar");
    if (radarContainer) radarContainer.innerHTML = '';
    if (calendarContainer) calendarContainer.innerHTML = '';
}

/**
 * Tears down the D3 Objectives Tree to prevent state conflicts.
 */
function teardownObjectivesTree() {
    console.log("Tearing down Objectives Tree.");
    const treeContainer = document.getElementById("tree-container");
    if (treeContainer) treeContainer.innerHTML = '';
}

function handleError(error) {
    console.error('Error:', error);
    let errorContainer = document.getElementById('global-error-message');
    if (!errorContainer) {
        errorContainer = document.createElement('div');
        errorContainer.id = 'global-error-message';
        errorContainer.className = 'error-message';
        document.querySelector('main').prepend(errorContainer);
    }
    errorContainer.textContent = `An error occurred: ${error.message}`;
    errorContainer.style.display = 'block';
    setTimeout(() => { errorContainer.style.display = 'none'; }, 5000);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
// #endregion