<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Rotating Doors</title>
<!-- jQuery CDN -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Three.js CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
<!-- Simplex Noise for noise generation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/simplex-noise/2.4.0/simplex-noise.min.js"></script>
<!-- Chroma.js for color manipulation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.1.0/chroma.min.js"></script>

    <!-- Font Awesome CDN -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

<link rel="stylesheet" href="/static/assets/fake.css">

</head>
<body>

    <header>
        <div id="logo"><i class="fas fa-globe"></i> Logo</div>
        <nav id="navigation">
            <a href="#"><i class="fas fa-home"></i></a>
            <a href="#"><i class="fas fa-briefcase"></i></a>
            <a href="#"><i class="fas fa-envelope"></i></a>
            <a href="#"><i class="fas fa-info-circle"></i></a>
        </nav>
        <div id="user-icon"><i class="fas fa-user"></i></div>
    </header>
    
    <canvas id="background">
    </canvas>
<div class="container" id='content-container'>

</div>

    <div id="doors">
        <div class="doorWrapper">
          <div class="door d1">
            <div class="doorFace front">
                <div class="in" data-destination="1">in</div>
            </div>
            <div class="doorFace back">
                <div class="out" data-destination="1">out</div>
            </div>
          </div>
        </div>
        <div class="doorWrapper">
          <div class="door d2">
            <div class="doorFace front">
                <div class="in" data-destination="1">in</div>
            </div>
            <div class="doorFace back">
                <div class="out" data-destination="1">out</div>
            </div>
          </div>
        </div>
        <div class="doorWrapper">
          <div class="door d3">
            <div class="doorFace front">
                <div class="in" data-destination="1">in</div>
            </div>
            <div class="doorFace back">
                <div class="out" data-destination="1">out</div>
            </div>
          </div>
        </div>
        <div class="doorWrapper">
          <div class="door d4">
            <div class="doorFace front">
                <div class="in" data-destination="1">in</div>
            </div>
            <div class="doorFace back">
                <div class="out" data-destination="1">out</div>
            </div>
          </div>
        </div>
    </div>
    
   <!-- GSAP CDN -->
   <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
   <script src="/static/assets/script3.js"></script>
   <script src="/static/assets/control_2024.js"></script>

</body>

</html>
