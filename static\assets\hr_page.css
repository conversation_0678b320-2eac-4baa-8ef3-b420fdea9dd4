/* Brutalist CSS */
body {
  font-family: 'Open Sans', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  margin: 0;
  padding: 0;
}

header {
  background-color: #000;
  color: #fff;
  padding: 1rem;
  text-align: center;
  font-size: 1.5rem;
}

#navigation {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

#navigation a {
  color: #fff;
  text-decoration: none;
  font-size: 1rem;
}

main {
  padding: 2rem;
}

#button-row {
  display: flex;
  justify-content: space-around;
  margin-bottom: 2rem;
}

button {
  background-color: #000;
  color: #fff;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #333;
}

.section-content {
  background-color: #fff;
  padding: 2rem;
  border: 2px solid #000;
  margin-bottom: 2rem;
}

form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

label {
  font-weight: bold;
}

input, select, textarea {
  padding: 0.5rem;
  border: 2px solid #000;
  font-size: 1rem;
}

hr {
  border: 2px solid #000;
  margin: 2rem 0;
}




/* Radar Chart Styles */
#skillsRadarChart {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.radar-area {
  stroke-width: 2px;
  mix-blend-mode: multiply;
}

.grid-circle {
  stroke: #ddd;
  stroke-width: 0.5px;
}

.axis-label {
  font-size: 0.8em;
  fill: #666;
}

.chart-error {
  padding: 20px;
  background: #ffe6e6;
  border: 1px solid #ffcccc;
  border-radius: 4px;
  color: #cc0000;
  margin: 20px 0;
}

/* Two-column layout for chart and tooltip */
#employee-skills-chart {
  display: flex;
  align-items: flex-start;
}

.radar-container {
  flex: 3; /* Larger column for the graph */
  margin-right: 20px; /* Space between the columns */
}

.radar-tooltip-container {
  flex: 1; /* Smaller column for the tooltip */
  /* background-color: #f5f5f5; */
  background-color: #fff;
  padding: 10px;
  /* border: 1px solid #ccc; */
  border-radius: 5px;
}

.radar-tooltip-content p {
  margin: 5px 0;
}



/* ============================================= */
/* Brutalist Animated Task Tracker Styles       */
/* ============================================= */

/* Main container for all task trackers */
#animated-task-tracker-container {
    display: flex;
    flex-direction: column;
    gap: 4rem; /* More space between tasks */
    padding: 1rem;
    overflow-x: auto; /* Allow horizontal scrolling for very long trackers */
}

/* Container for a single task's tracker */
.task-tracker {
    width: 100%;
}

.task-tracker h4 {
    font-family: 'Courier New', Courier, monospace;
    font-size: 1.2rem;
    text-transform: uppercase;
    margin-bottom: 2rem;
    border-bottom: 2px solid #000;
    padding-bottom: 0.5rem;
}

/* The horizontal list for the steps */
.kpa-stepper {
    display: flex;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
}

/* A single step (sphere and label) in the tracker */
.kpa-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    text-align: center;
    flex-shrink: 0; /* Prevents steps from shrinking */
    width: 150px; /* Gives each step a fixed width */
    opacity: 0; /* Initially hidden for animation */
    transform: translateY(20px); /* Start slightly lower for animation */
    transition: opacity 0.5s ease, transform 0.5s ease;
}

/* The connecting line before each step (except the first) */
.kpa-step::before {
    content: '';
    position: absolute;
    top: 25px; /* Vertically center with the sphere */
    right: 32%;
    width: 100%;
    height: 2px;
    background-color: #000;
    z-index: -1;
    transform: scaleX(0); /* Initially invisible */
    transform-origin: left;
    transition: transform 0.8s ease-in-out;
}

/* Hide the line for the very first step */
.kpa-stepper li:first-child::before {
    display: none;
}

/* The sphere element */
.kpa-sphere {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #fff;
    border: 3px solid #000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.3s;
    position: relative;
    z-index: 1; /* Ensure sphere is above the line */
}

.kpa-sphere:hover {
    transform: scale(1.15);
}

/* KPA text label */
.kpa-label {
    margin-top: 1rem;
    font-size: 0.9rem;
    font-weight: bold;
    color: #000;
    width: 100%;
    word-wrap: break-word;
}

/* --- Status Colors --- */
.kpa-step.completed .kpa-sphere {
    background-color: #4caf50; /* Green */
    color: #fff;
}
.kpa-step.revisions-needed .kpa-sphere {
    background-color: #ffeb3b; /* Yellow */
}
.kpa-step.under-review .kpa-sphere {
    background-color: #2196f3; /* Blue */
    color: #fff;
}
.kpa-step.not-started .kpa-sphere {
    background-color: #fff;
}

/* --- Animation Trigger --- */
/* When this class is added, the animation starts */
.kpa-step.is-visible {
    opacity: 1;
    transform: translateY(0);
}

.kpa-step.is-visible::before {
    transform: scaleX(1);
}


/* ============================================= */
/* Brutalist Animated Task Tracker Styles End      */
/* ============================================= */



/* --- Jewel Fragment Chart Styles --- */

#gamification-container {
    background: radial-gradient(circle at center, #1a1a2e 0%, #0f0f1e 100%);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #444;
    overflow: hidden; /* Important for containing effects */
}

.chart-container {
    position: relative;
    display: flex; /* This allows side-by-side layout */
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 800px; /* Adjust as needed */
    margin: 0 auto;
    padding: 20px;
    box-sizing: border-box;
}



/* .chart-container {
    position: relative;
    width: 100%;
    height: 600px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
} */

.donut-chart {
    width: 400px;
    height: 400px;
    position: relative;
    animation: float 6s ease-in-out infinite;
}
.orbit-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 320px;
    height: 320px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: ring-pulse 4s ease-in-out infinite;
}
.orbit-ring::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border: 1px solid rgba(108, 92, 231, 0.2);
    border-radius: 50%;
    animation: ring-glow 3s ease-in-out infinite alternate;
}
.jewel-fragment {
    position: absolute;
    width: 24px;
    height: 24px;
    transform-origin: center;
    cursor: pointer;
    transition: all 0.3s ease;
}
.jewel-fragment:hover {
    transform: scale(1.1);
    filter: brightness(1.3) drop-shadow(0 0 20px currentColor);
}
.jewel-fragment svg {
    width: 24px;
    height: 24px;
}
.jewel-fragment polygon {
    stroke-width: 1;
    stroke: rgba(255, 255, 255, 0.4);
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.5));
    transition: all 0.3s ease;
}
.progress-fragment polygon {
    fill: #2d3436; /* Using a solid color as gradients in SVG are complex */
    animation: pulse-dim 4s ease-in-out infinite;
    opacity: 0.4;
}
.progress-fragment.lit polygon {
    fill: #fdcb6e; /* Using a solid color */
    animation: pulse-active 2s ease-in-out infinite;
    opacity: 1;
}
.center-gem {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    animation: rotate-center 8s linear infinite;
}
.center-gem polygon {
    fill: #fdcb6e;
    stroke: rgba(255, 255, 255, 0.5);
    stroke-width: 2;
    filter: brightness(0.3) drop-shadow(0 0 5px rgba(253, 203, 110, 0.3));
}
/* .rank-info {
    position: absolute;
    left: 0px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 14px;
    backdrop-filter: blur(10px);
    width: 150px;
    z-index: 10;
} */


/* --- Positioning for the Rank Info Panel --- */
/* (This class is reused by both donut and cone) */
.rank-info {
    width: 200px; /* Give the legend a fixed width */
    margin-right: 20px; /* Space between legend and chart */
    flex-shrink: 0; /* Prevents the legend from shrinking */
    
    /* These styles are from your example and are good */
    position: relative; /* Change from absolute to relative for flexbox layout */
    transform: none; /* Remove transform */
    left: auto; top: auto; /* Remove positioning */
    
    background: rgba(0, 0, 0, 0.8);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 14px;
    backdrop-filter: blur(10px);
    z-index: 10;
}







.rank-info h3 {
    margin-top: 0;
    color: #ffeaa7;
    font-size: 16px;
}
.current-rank {
    color: #6c5ce7;
    font-weight: bold;
    font-size: 16px;
    margin: 10px 0;
}
.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin: 10px 0;
    overflow: hidden;
}
.progress-fill {
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, #ffeaa7, #fdcb6e);
    border-radius: 4px;
    transition: width 2s ease;
}
.rank-list {
    font-size: 12px;
    margin-top: 15px;
}
.rank-item {
    display: flex;
    justify-content: space-between;
    margin: 4px 0;
    opacity: 0.7;
}
.rank-item.current {
    opacity: 1;
    color: #6c5ce7;
    font-weight: bold;
}
.rank-item.achieved {
    opacity: 1;
    color: #00b894;
}
.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}
.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    animation: float-particle 12s linear infinite;
}
.glow-effect {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(108, 92, 231, 0.05) 0%, transparent 70%);
    animation: glow 4s ease-in-out infinite alternate;
    pointer-events: none;
}

/* --- Keyframe Animations --- */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-8px) rotate(1deg); }
}
@keyframes ring-pulse {
    0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.02); }
}
@keyframes ring-glow {
    from { opacity: 0.1; }
    to { opacity: 0.4; }
}
@keyframes pulse-dim {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.5; }
}
@keyframes pulse-active {
    0%, 100% { opacity: 0.9; }
    50% { opacity: 1; }
}
@keyframes rotate-center {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}
@keyframes float-particle {
    0% { transform: translateY(400px) translateX(0px); opacity: 0; }
    10% { opacity: 0.8; }
    90% { opacity: 0.8; }
    100% { transform: translateY(-100px) translateX(30px); opacity: 0; }
}
@keyframes glow {
    from { opacity: 0.2; transform: scale(0.98); }
    to { opacity: 0.4; transform: scale(1.02); }
}

/* --- Container for the 3D Canvas --- */
.cone-canvas-container {
    flex-grow: 1; /* Allows the canvas to take available space */
    height: 500px; /* Give it a fixed height */
    position: relative;
}

.cone-canvas-container canvas {
    display: block; /* Removes any extra space below the canvas */
    width: 100% !important; /* Overrides Three.js inline style */
    height: 100% !important;
}






/* --- Objective Weight Input in D3 Tree --- */
.objective-weight-input {
    width: 50px;
    margin-left: 10px;
    padding: 2px 4px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #f9f9f9;
}

/* --- Rank Settings Form --- */
#rank-settings-form {
    padding: 15px;
    background-color: #2a2a2e;
    border-radius: 8px;
    margin-top: 15px;
}
.rank-input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}
.rank-input-group input {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #555;
    background-color: #333;
    color: #fff;
    flex-grow: 1;
}
.rank-input-group button {
    padding: 8px 12px;
    border: none;
    background-color: #c44;
    color: white;
    border-radius: 4px;
    cursor: pointer;
}
#add-rank-btn {
    margin-right: 10px;
}

/* ============================================= */
/* --- Gamification Visualizations END --- */
/* ============================================= */
