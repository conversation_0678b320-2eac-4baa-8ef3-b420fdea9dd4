<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>'s Progress - Jewel Fragment Chart</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: radial-gradient(circle at center, #1a1a2e 0%, #0f0f1e 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        .chart-container {
            position: relative;
            width: 600px;
            height: 600px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .donut-chart {
            width: 400px;
            height: 400px;
            position: relative;
            animation: float 6s ease-in-out infinite;
        }
        .orbit-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 320px;
            height: 320px;
            transform: translate(-50%, -50%);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: ring-pulse 4s ease-in-out infinite;
        }
        .orbit-ring::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            border: 1px solid rgba(108, 92, 231, 0.2);
            border-radius: 50%;
            animation: ring-glow 3s ease-in-out infinite alternate;
        }
        .jewel-fragment {
            position: absolute;
            width: 24px;
            height: 24px;
            transform-origin: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .jewel-fragment:hover {
            transform: scale(1.1);
            filter: brightness(1.3) drop-shadow(0 0 20px currentColor);
        }
        .jewel-fragment svg {
            width: 24px;
            height: 24px;
        }
        .jewel-fragment polygon {
            stroke-width: 1;
            stroke: rgba(255, 255, 255, 0.4);
            filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.5));
            transition: all 0.3s ease;
        }
        .progress-fragment polygon {
            fill: linear-gradient(135deg, #2d3436, #636e72);
            animation: pulse-dim 4s ease-in-out infinite;
            opacity: 0.4;
        }
        .progress-fragment.lit polygon {
            fill: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            animation: pulse-active 2s ease-in-out infinite;
            opacity: 1;
        }
        .center-gem {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            animation: rotate-center 8s linear infinite;
        }
        .center-gem polygon {
            fill: linear-gradient(45deg, #ffeaa7, #fdcb6e);
            stroke: rgba(255, 255, 255, 0.5);
            stroke-width: 2;
            filter: brightness(0.3) drop-shadow(0 0 5px rgba(253, 203, 110, 0.3));
        }
        .rank-info {
            position: absolute;
            left: -180px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
            backdrop-filter: blur(10px);
            width: 150px;
        }
        .rank-info h3 {
            margin-top: 0;
            color: #ffeaa7;
            font-size: 16px;
        }
        .current-rank {
            color: #6c5ce7;
            font-weight: bold;
            font-size: 16px;
            margin: 10px 0;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-fill {
            width: 0%;
            height: 100%;
            background: linear-gradient(90deg, #ffeaa7, #fdcb6e);
            border-radius: 4px;
            transition: width 2s ease;
        }
        .rank-list {
            font-size: 12px;
            margin-top: 15px;
        }
        .rank-item {
            display: flex;
            justify-content: space-between;
            margin: 4px 0;
            opacity: 0.7;
        }
        .rank-item.current {
            opacity: 1;
            color: #6c5ce7;
            font-weight: bold;
        }
        .rank-item.achieved {
            opacity: 1;
            color: #00b894;
        }
        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            animation: float-particle 12s linear infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-8px) rotate(1deg); }
        }
        @keyframes ring-pulse {
            0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.02); }
        }
        @keyframes ring-glow {
            from { opacity: 0.1; }
            to { opacity: 0.4; }
        }
        @keyframes pulse-dim {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.5; }
        }
        @keyframes pulse-active {
            0%, 100% { opacity: 0.9; }
            50% { opacity: 1; }
        }
        @keyframes rotate-center {
            from { transform: translate(-50%, -50%) rotate(0deg); }
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }
        @keyframes float-particle {
            0% {
                transform: translateY(400px) translateX(0px);
                opacity: 0;
            }
            10% {
                opacity: 0.8;
            }
            90% {
                opacity: 0.8;
            }
            100% {
                transform: translateY(-100px) translateX(30px);
                opacity: 0;
            }
        }
        .glow-effect {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(108, 92, 231, 0.05) 0%, transparent 70%);
            animation: glow 4s ease-in-out infinite alternate;
        }
        @keyframes glow {
            from { opacity: 0.2; transform: scale(0.98); }
            to { opacity: 0.4; transform: scale(1.02); }
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <div class="glow-effect"></div>

        <div class="rank-info">
            <h3>ANGELA'S PROGRESS</h3>
            <div class="current-rank">Manager</div>
            <div style="font-size: 12px; color: #999;">Current: 7,500 pts</div>
            <div style="font-size: 12px; color: #999;">Next: 15,000 pts</div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div style="font-size: 11px; text-align: center; color: #999;">0% to Senior Manager</div>

            <div class="rank-list">
                <div class="rank-item achieved">Officer (0)</div>
                <div class="rank-item achieved">Asst Manager (1k)</div>
                <div class="rank-item achieved">Deputy Manager (3k)</div>
                <div class="rank-item current">Manager (7.5k)</div>
                <div class="rank-item">Senior Manager (15k)</div>
                <div class="rank-item">Principal Manager (25k)</div>
                <div class="rank-item">Chief Manager (40k)</div>
                <div class="rank-item">Deputy Director (80k)</div>
                <div class="rank-item">Director (120k)</div>
            </div>
        </div>
        <div class="donut-chart">
            <div class="orbit-ring"></div>

            <svg width="400" height="400" viewBox="0 0 400 400" style="position: absolute;">
                <!-- Progress fragments arranged in a perfect circle -->
                <!-- Fragment positions calculated using trigonometry -->
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(0deg) translate(160px) rotate(0deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(20deg) translate(160px) rotate(-20deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(40deg) translate(160px) rotate(-40deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(60deg) translate(160px) rotate(-60deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(80deg) translate(160px) rotate(-80deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(100deg) translate(160px) rotate(-100deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(120deg) translate(160px) rotate(-120deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(140deg) translate(160px) rotate(-140deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(160deg) translate(160px) rotate(-160deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(180deg) translate(160px) rotate(-180deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(200deg) translate(160px) rotate(-200deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(220deg) translate(160px) rotate(-220deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(240deg) translate(160px) rotate(-240deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(260deg) translate(160px) rotate(-260deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(280deg) translate(160px) rotate(-280deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(300deg) translate(160px) rotate(-300deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(320deg) translate(160px) rotate(-320deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
                <g class="jewel-fragment progress-fragment" style="transform: translate(200px, 200px) rotate(340deg) translate(160px) rotate(-340deg);">
                    <svg>
                        <polygon points="12,2 20,8 18,18 6,18 4,8" />
                    </svg>
                </g>
            </svg>
            <!-- Center gem -->
            <div class="center-gem">
                <svg width="80" height="80" viewBox="0 0 80 80">
                    <polygon points="40,8 65,25 60,55 20,55 15,25" />
                    <polygon points="40,18 55,30 50,45 30,45 25,30" fill="rgba(255,255,255,0.2)" />
                </svg>
            </div>
        </div>
        <!-- Floating particles -->
        <div class="floating-particles">
            <div class="particle" style="left: 15%; animation-delay: 0s;"></div>
            <div class="particle" style="left: 25%; animation-delay: 3s;"></div>
            <div class="particle" style="left: 35%; animation-delay: 6s;"></div>
            <div class="particle" style="left: 45%; animation-delay: 1s;"></div>
            <div class="particle" style="left: 55%; animation-delay: 4s;"></div>
            <div class="particle" style="left: 65%; animation-delay: 7s;"></div>
            <div class="particle" style="left: 75%; animation-delay: 2s;"></div>
            <div class="particle" style="left: 85%; animation-delay: 5s;"></div>
        </div>
    </div>
    <script>
        // Data from the provided JSON
        const gameData = {
            employee: "Angela",
            currentRank: "Manager",
            currentPoints: 7500,
            nextRank: "Senior Manager",
            nextRankPoints: 15000,
            progressPercentage: 0,
            totalPoints: 0
        };

        // Update progress bar based on actual progress
        const progressFill = document.getElementById('progressFill');
        setTimeout(() => {
            progressFill.style.width = gameData.progressPercentage + '%';
        }, 1000);

        // Calculate progress and light up fragments accordingly
        const progressPercentage = gameData.progressPercentage; // 0% in this case
        const totalFragments = 18;
        const fragmentsToLight = Math.floor((progressPercentage / 100) * totalFragments);

        // Light up fragments based on progress
        const fragments = document.querySelectorAll('.progress-fragment');
        for (let i = 0; i < fragmentsToLight; i++) {
            fragments[i].classList.add('lit');
        }

        // Update center gem brightness based on progress
        const centerGem = document.querySelector('.center-gem polygon');
        const baseBrightness = 0.3; // Minimum brightness
        const maxBrightness = 1.2; // Maximum brightness at 100%
        const currentBrightness = baseBrightness + (progressPercentage / 100) * (maxBrightness - baseBrightness);
        const glowIntensity = progressPercentage / 100;
        centerGem.style.filter = `brightness(${currentBrightness}) drop-shadow(0 0 ${5 + glowIntensity * 15}px rgba(253, 203, 110, ${0.3 + glowIntensity * 0.7}))`;

        // Add interactive hover effects
        document.querySelectorAll('.jewel-fragment').forEach(fragment => {
            fragment.addEventListener('mouseenter', function() {
                this.style.transform = this.style.transform.replace('scale(1.1)', '') + ' scale(1.2)';
                this.style.zIndex = '10';
            });

            fragment.addEventListener('mouseleave', function() {
                this.style.transform = this.style.transform.replace(' scale(1.2)', '');
                this.style.zIndex = '1';
            });

            fragment.addEventListener('click', function() {
                // Create sparkle effect on click
                for (let i = 0; i < 6; i++) {
                    const sparkle = document.createElement('div');
                    sparkle.style.cssText = `
                        position: absolute;
                        width: 4px;
                        height: 4px;
                        background: white;
                        border-radius: 50%;
                        pointer-events: none;
                        left: ${Math.random() * 100}%;
                        top: ${Math.random() * 100}%;
                        animation: sparkle 1.2s ease-out forwards;
                    `;
                    this.appendChild(sparkle);
                    setTimeout(() => sparkle.remove(), 1200);
                }
            });
        });

        // Add sparkle animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes sparkle {
                0% { transform: scale(0) rotate(0deg); opacity: 1; }
                100% { transform: scale(2) rotate(180deg); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Dynamic particle generation
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (Math.random() * 4 + 10) + 's';
            particle.style.animationDelay = Math.random() * 2 + 's';

            document.querySelector('.floating-particles').appendChild(particle);

            setTimeout(() => {
                particle.remove();
            }, 14000);
        }

        // Generate particles periodically
        setInterval(createParticle, 4000);

        // Add orbit ring interaction
        document.querySelector('.orbit-ring').addEventListener('click', function() {
            this.style.animation = 'none';
            setTimeout(() => {
                this.style.animation = 'ring-pulse 4s ease-in-out infinite';
            }, 100);
        });
    </script>
</body>
</html>
