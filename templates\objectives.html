<!DOCTYPE html>
<html>
<head>
	<title></title>
  <script src="https://code.jquery.com/jquery-1.10.2.js"></script>

  <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">
	  <!-- Font Awesome Icons -->
	  <script src="https://kit.fontawesome.com/42d5adcbca.js" crossorigin="anonymous"></script>
	  <link href="../assets/css/nucleo-svg.css" rel="stylesheet" />
	<style>
		body {
			
			font-family: Arial, sans-serif;
			font-weight: lighter;
			margin: 0;
			padding: 0;
		}
		
 /* Add the following CSS for the sidebar */
 .sidebar {
      position: fixed;
      top: 0;
      left: 0;
      width: 250px;
      height: 100vh;
      background-color: #f5f5f5;
      transition: transform 0.3s ease-in-out;
      transform: translateX(-100%);
    }

    .sidebar.open {
      transform: translateX(0);
    }

    .hamburger {
      display: block;
      width: 30px;
      height: 30px;
      cursor: pointer;
      position: relative;
      z-index: 999;
    }

    .hamburger span {
      display: block;
      width: 100%;
      height: 2px;
      background-color: #333;
      position: absolute;
      left: 0;
      transition: transform 0.3s ease-in-out;
    }

    .hamburger span:nth-child(1) {
      top: 0;
    }

    .hamburger span:nth-child(2) {
      top: 50%;
      transform: translateY(-50%);
    }

    .hamburger span:nth-child(3) {
      bottom: 0;
    }

    .hamburger.open span:nth-child(1) {
      transform: translateY(50%) rotate(45deg);
    }

    .hamburger.open span:nth-child(2) {
      transform: translateY(-50%) rotate(45deg);
    }

    .hamburger.open span:nth-child(3) {
      transform: translateY(-50%) rotate(-45deg);
    }

		.search-container {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 10px;
			background: rgba(221, 216, 228, 0.5);
			border-radius: 10px;
			backdrop-filter: blur(1px);
			}

			input[type="text"] {
			padding: 10px;
			border: none;
			border-radius: 5px;
			outline: none;
			background: rgba(221, 216, 228, 0.5);
			backdrop-filter: blur(1px);
			margin-right: 10px;
			width: 200px;
			box-shadow: 0px 0px 10px rgba(255, 255, 255, 0.5),
						inset 0px 0px 10px rgba(0, 0, 0, 0.1);
			}

			button[type="submit"] {
			padding: 10px 20px;
			background: #fff;
			border: none;
			border-radius: 5px;
			cursor: pointer;
			outline: none;
			box-shadow: 0px 0px 10px rgba(255, 255, 255, 0.5),
						inset 0px 0px 10px rgba(0, 0, 0, 0.1);
			transition: transform 0.2s ease;
			}

			button[type="submit"]:hover {
			transform: scale(1.05);
			}



			.navbar {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 10px;
				background-color: rgba(221, 216, 228, 0.5);
				backdrop-filter: blur(1px);
				-webkit-backdrop-filter: blur(1px);
				border-radius: 10px;
				box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
				border: 10px transparent rgba(253, 253, 253, 0.97);
				}

.navbar-brand {
  display: flex;
  align-items: center;
}

.navbar-title {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}

.line1 {
  font-size: 20px;
  font-weight: bold;
  background: -webkit-linear-gradient(45deg, #ecf0f0, #659879);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}

.line2 {
	font-weight: 100;
	color: aliceblue;
  font-size: 14px;
  /* font-style: italic; */
}

.navbar-icons {
  display: flex;
  align-items: center;
}

.navbar-icon, .navbar-avatar {
  margin-left: 10px;
}

.navbar-icon i, .navbar-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}


.avatar-button {
  border: none;
  outline: none;
  background-color: transparent;
  cursor: pointer;
}

.avatar-button:hover {
  opacity: 0.8;
}

.avatar-button img {
  display: block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
}


		.hero {
			background-image: url('/static/image/jonny-gios-48RcnYWuZV8-unsplash.jpg');
			background-size: cover;
			background-position: center;
			height: 500px;
			position: relative;
			border-radius: 0% 0% 2% 2%;
			}
			.hero-text {
				width: 10%;
				padding: 10px;
				/* background-color: rgba(255, 255, 255, 0.8); */
				}

				.hero-text h1 {
				font-size: 2rem;
				font-weight: bold;
				margin-bottom: 10px;
				}

				.hero-text p {
				font-size: 1.5rem;
				margin-bottom: 0;
				}

				@media (max-width: 768px) {
				.hero-text {
					width: 80%;
				}
				}

					.container {
			max-width: 800px;
			margin: 0 auto;
			text-align: center;
			padding: 100px 20px;
			color: #fff;
			}

			h1 {
			font-size: 48px;
			margin-bottom: 20px;
			}

			p {
			font-size: 20px;
			line-height: 1.5;
			margin-bottom: 30px;
			}

			.btn {
			display: inline-block;
			padding: 10px 20px;
			border-radius: 25px;
			background-color: #fff;
			color: #000;
			font-weight: bold;
			text-transform: uppercase;
			transition: background-color 0.3s ease;
			}

			.btn:hover {
			background-color: #000;
			color: #fff;
			}

			.float-btn {
			position: absolute;
			bottom: 30px;
			right: 30px;
			width: 50px;
			height: 50px;
			background-color: #fff;
			border-radius: 50%;
			display: flex;
			justify-content: center;
			align-items: center;
			box-shadow: 0px 2px 10px rgba(121, 121, 20, 0.2);
			transition: transform 0.3s ease;
			}

			.float-btn:hover {
			transform: translateY(-5px);
			}

			.fa {
			font-size: 24px;
			color: #848419;
			}


		.row-special {
			display: inline-flex;
			/* flex-wrap: wrap;  */
			justify-content: space-evenly;
			/* background: linear-gradient(to bottom right, #b9dfda, #3f196c); */
			padding: 10px;
  			margin: -10px;
			}

			.card {
				width: 100px;
				height: 200px;
				padding: 20px;
				background-color: rgba(255, 255, 255, 0.02);
				border-radius: 10px;
				box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
				backdrop-filter: blur(10px);
				-webkit-backdrop-filter: blur(10px);
				border: 1px solid rgba(255, 255, 255, 0.18);
				}

				.card-title {
				font-size: 18px;
				font-weight: 100;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				max-width: 100%;
				color: aliceblue;
				/* background: -webkit-linear-gradient(45deg, #fc5c7d, #6a82fb); */
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				box-decoration-break: clone;
				-webkit-box-decoration-break: clone;
				}

				.card-body {
				font-size: 16px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				max-width: 100%;
				}



			.card:hover {
			transform: translateY(-5px);
			}

			.card h2 {
			font-size: 24px;
			font-weight: 100;
			margin-bottom: 10px;
			text-align: center;
			}

			.card p {
			font-size: 16px;
			line-height: 1.5;
			text-align: center;
			}


/* From https://css.glass */


		
		.kanban-section {
			border-radius: -100px -100px 0px 0px;
			display: flex;
			justify-content: center;
			align-items: flex-start;
			flex-wrap: wrap;
			/* background: linear-gradient(to bottom right, #32d2a2f7, #993abb); */
			background: linear-gradient(160deg, rgb(51, 241, 172), rgb(16, 11, 62));
			padding: 50px;
			background-color: #fff;
		}

		.button-row {
			padding: 50px;
  		/* display: block; */
		justify-content: center;
		align-items: flex-start;
		gap: 10px;
		}


.glassmorphic-button {
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 25px;
  color: white;
  cursor: pointer;
  font-size: 16px;
  font-weight: lighter;
  padding: 10px 20px;
  transition: background-color 0.5s ease;
}

.glassmorphic-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.transactions {
  background-color: rgb(41, 38, 95);
  box-shadow: 0 5px 15px rgb(78, 72, 135);
}

.conversations {
  background-color: #00b894;
  box-shadow: 0 5px 15px rgba(0, 184, 148, 0.4);
} */

/* datatable stuff */

.container {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  padding: 20px;
  margin: 50px auto;
  width: 80%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

input[type="text"] {
  background-color: transparent;
  border: none;
  border-bottom: 2px solid rgba(252, 252, 252, 0.5);
  color: white;
  font-size: 16px;
  padding: 10px;
  margin-bottom: 20px;
  margin-top: 40px;
  width: 100%;
  max-width: 400px;
}

input[type="text"]::placeholder {
  color: rgba(0, 0, 0, 0.5);
}

table {
  border-collapse: collapse;
  margin-top: 20px;
  width: 100%;
  max-width: 800px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  overflow: hidden;
  border-radius: 20px;
}

th, td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

th {
  background-color: #250953;
  color: white;
}

tr:nth-child(even) {
  background-color: #f2f2f2;
}

tr:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* modal */

.login-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 0 16px rgba(0, 0, 0, 0.1);
  padding: 32px;
  max-width: 480px;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  z-index: 9999;
}




.modal-content {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  z-index: 2;
  position: relative;
}

.modal-content h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #4a4e69;
  font-weight: 700;
}

form {
  display: flex;
  flex-direction: column;
}

label {
  font-size: 16px;
  margin-bottom: 8px;
  color: #4a4e69;
  font-weight: 700;
}

input {
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 10px;
  border: none;
  background-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

button {
  background-color: #4a4e69;
  color: #fff;
  padding: 10px 20px;
  border-radius: 10px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

button:hover {
  background-color: #3c3f58;
}

.background-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
}

.background-svg svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.background-svg path {
  fill: #fff;
}

/* modal end */

	</style>
</head>
<body>
    <div class="sidebar">
      <!-- Sidebar content goes here -->
    </div>
  
    <section class="hero">
      <nav class="navbar">
        <div class="navbar-brand">
          <div class="hamburger">
            <span></span>
            <span></span>
            <span></span>
          </div>
  
          <div class="navbar-title">
            <span class="line1">Welcome, Person</span>
            <span class="line2">What will you do today?</span>
          </div>
        </div>
        <div class="navbar-icons">
          <div class="navbar-icon">
            <i class="fas fa-bell"></i>
          </div>
  
          <button class="navbar-avatar avatar-button" id="login-button" data-toggle="modal" data-target="#login-modal">
            <img src="/static/image/12.jpg" alt="User Avatar">
          </button>
        </div>
      </nav>
		<div class="container">

			<div class="search-container">
				<input type="text" placeholder="Search...">
				<button type="submit">Search</button>
			  </div>
		  </div>
		  <a href="#" class="float-btn"><i class="fa fa-plus"></i></a>

	</section>

	
	<div class="kanban-section">

		<div class="row-special">
			<div class="col-special">
				<div class="card">
					<center>
						<h4 style="color: aliceblue; font-weight: 100;">My Wallet</h4>
						<h2 style="color: aliceblue; font-weight: 100;">GHC320</h2>
					</center>
				</div>
			</div>

		<div class="col-special">
			<div class="card">
				<center>
					<h4 style="color: aliceblue; font-weight: 100;">Total Engagements</h4>
					<h1 style="color: aliceblue; font-weight: 100;">32</h1>
				</center>
			</div>
		</div>
			
		<div class="col-special">
			<div class="card">
				<div class="card-title">New Leads</div>			
				<center>

					<!-- <h4 style="color: aliceblue; font-weight: 100;">New Leads</h4> -->
					<h1 style="color: aliceblue; font-weight: 100;">3</h1>
				</center>
			</div>
			</div>
		</div>


		<div class="button-row">
			<button class="glassmorphic-button transactions" id="button1">Transactions</button>
			<button class="glassmorphic-button conversations" id="button2">Conversations</button>
			<div class="hidden" name ="holder" id=""></div>
		  </div>


		  <!-- <div class="container"> -->
		
			<table id="table1">
				<input type="text" id="search-input" placeholder="Search...">
			  <thead>
				<tr>
				  <th>Name</th>
				  <th>Age</th>
				  <th>Gender</th>
				</tr>
			  </thead>
			  <tbody>
				<tr>
				  <td>John</td>
				  <td>25</td>
				  <td>Male</td>
				</tr>
				<tr>
				  <td>Jane</td>
				  <td>30</td>
				  <td>Female</td>
				</tr>
				<tr>
				  <td>Bob</td>
				  <td>42</td>
				  <td>Male</td>
				</tr>
			  </tbody>
			</table>
	
			<table id="table2">
				<thead>
				  <tr>
					<th>Name</th>
					<th>Age</th>
					<th>Gender</th>
				  </tr>
				</thead>
				<tbody>
				  <tr>
					<td>John</td>
					<td>25</td>
					<td>Male</td>
				  </tr>
				  <tr>
					<td>Jane</td>
					<td>30</td>
					<td>Female</td>
				  </tr>
				</tbody>
			  </table>
		
		  <!-- </div> -->
		 
    </div>


	<div class="login-modal" id="login-modal">
		<div class="modal-content" role="document">
		  <h2>Login</h2>
		  <form>
			<label for="username">Username:</label>
			<input type="text" id="username" name="username" required>
			<label for="password">Password:</label>
			<input type="password" id="password" name="password" required>
			<button type="submit">Log in</button>
		  </form>
		</div>
		<div class="background-svg">
		  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="#fff" fill-opacity="1" d="M0,288L48,288C96,288,192,288,288,245.3C384,203,480,117,576,90.7C672,64,768,96,864,101.3C960,107,1056,85,1152,74.7C1248,64,1344,64,1392,64L1440,64L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>
		</div>
	  </div>
	  


<script>
	  $(document).ready(function() {
		$('#table1').hide();
		  $('#table2').hide();
		//   var hold = $('div[name="holder"]');

		// Toggle tables when buttons are clicked
		$('#button1').click(function() {
		  $('#table1').show();
		  $('#table2').hide();
		  $('#button1').addClass('active');
		  $('#button2').removeClass('active');
		  $('div[name="holder"]').attr("id", 1);
		});
	  
		$('#button2').click(function() {
		  $('#table2').show();
		  $('#table1').hide();
		  $('#button2').addClass('active');
		  $('#button1').removeClass('active');
		  $('div[name="holder"]').attr("id", 2);

		});
	  });

</script>


<script>
	const searchInput = document.getElementById('search-input');
	const held = document.querySelector("[name='holder']");
	
	var table="";
	var tableRows="";
	// const table = document.getElementById('table1');
	// const tableRows = table.getElementsByTagName('tr');

	
	
	searchInput.addEventListener('keyup', function() {
		
		if (held.id==1) {
		console.log("table1");
			table = document.getElementById('table1');
			tableRows = table.getElementsByTagName('tr');
		} else {
			console.log("table2");
			table = document.getElementById('table2');
			tableRows = table.getElementsByTagName('tr');
		}
	  const searchValue = this.value.toLowerCase();
	  for (let i = 1; i < tableRows.length; i++) {
		const rowCells = tableRows[i].getElementsByTagName('td');
		let rowContainsValue = false;
		for (let j = 0; j < rowCells.length; j++) {
		  const cellValue = rowCells[j].textContent.toLowerCase();
		  if (cellValue.includes(searchValue)) {
			rowContainsValue = true;
			break;
		  }
		}
		if (rowContainsValue) {
		  tableRows[i].style.display = '';
		} else {
		  tableRows[i].style.display = 'none';
		}
	  }
	});
</script>

<!-- modal load js -->
<script>
	  $(document).ready(function() {
		$('#login-modal').hide();
	  })

</script>


<script>
	const modal = document.getElementById("login-modal");
	const loginButton = document.getElementById("login-button");

	loginButton.addEventListener("click", () => {
	modal.style.display = "flex";
	setTimeout(() => {
		modal.style.opacity = 1;
	}, 100);
	});

	modal.addEventListener("click", (event) => {
	if (event.target === modal) {
		modal.style.opacity = 0;
		setTimeout(() => {
		modal.style.display = "none";
		}, 100);
	}
	});

	document.addEventListener("keydown", (event) => {
	if (event.key === "Escape") {
		modal.style.opacity = 0;
		setTimeout(() => {
		modal.style.display = "none";
		}, 300);
	}
	});

</script>
<script>
    document.addEventListener("DOMContentLoaded", function () {
      const hamburger = document.querySelector(".hamburger");
      const sidebar = document.querySelector(".sidebar");

      hamburger.addEventListener("click", function () {
        hamburger.classList.toggle("open");
        sidebar.classList.toggle("open");
      });
    });
  </script>
</body>
</html>
