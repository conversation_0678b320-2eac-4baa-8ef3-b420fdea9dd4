<!DOCTYPE html>
<html>
<head>
  <title>Radar Chart for Skills</title>
    <!-- jQ<PERSON>y CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Google Fonts -->
    <link href='http://fonts.googleapis.com/css?family=Open+Sans:400,300' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Raleway' rel='stylesheet' type='text/css'>

    <!-- <link rel="stylesheet" href="/static/assets/hr_page.css"> -->
    <link rel="stylesheet" type="text/css" href="/static/assets/cal/dist/calendar-heatmap.min.css">
</head>
<body>
  <div id="calendar">
  </div>
  <script>
    data = {
  "_id": "65f43c31d4257d8c1c4c69e5",
  "date_of_birth": "1/15/1982",
  "division": "Regulatory Administration",
  "experience": "10 years in operations",
  "first_name": "Angela",
  "gamification": {
    "all_ranks": [
      {
        "name": "Officer",
        "points": 0
      },
      {
        "name": "Assistant Manager",
        "points": 1000
      },
      {
        "name": "Deputy Manager",
        "points": 3000
      },
      {
        "name": "Manager",
        "points": 7500
      },
      {
        "name": "Senior Manager",
        "points": 15000
      },
      {
        "name": "Principal Manager",
        "points": 25000
      },
      {
        "name": "Chief Manager",
        "points": 40000
      },
      {
        "name": "Deputy Director",
        "points": 80000
      },
      {
        "name": "Director",
        "points": 120000
      }
    ],
    "current_rank": {
      "name": "Manager",
      "points": 7500
    },
    "is_at_highest_rank": false,
    "message": null,
    "next_rank": {
      "name": "Senior Manager",
      "points": 15000
    },
    "points_for_next_rank": 15000,
    "progress_percentage": 0,
    "total_points": 0
  },
  "gender": "Female",
  "heatmap_data": [
    {
      "date": "2023-04-05",
      "description": "Security assessment completed",
      "task_title": "Security System Upgrade",
      "value": 2.0
    },
    {
      "date": "2023-04-12",
      "description": "New security software installed",
      "task_title": "Security System Upgrade",
      "value": 4.0
    }
  ],
  "last_name": "Martinez",
  "password": "1234",
  "qualifications": "BSc Business Management",
  "rank": "Manager",
  "skills": [],
  "staff_id": "E004",
  "status": "Unit_member",
  "tasks": [
    {
      "_id": "679020666d2ab965df73d5e6",
      "assignment_date": "2023-04-05",
      "completion_date": "2023-05-10",
      "created_at": "2023-04-01T09:00:00Z",
      "expected_completion_date": "2023-05-10",
      "progress": [
        {
          "description": {
            "date": "2023-04-05",
            "text": "Security assessment completed"
          },
          "drafts": [
            {
              "comments": [
                {
                  "comment": "Assessment is thorough. Proceed with software installation.",
                  "comment_id": "comment_007",
                  "commented_at": "2023-04-05T11:30:00Z",
                  "commenter_id": "E008"
                }
              ],
              "content": "Security assessment report.",
              "draft_id": "draft_007",
              "reviewed_at": "2023-04-05T11:30:00Z",
              "reviewed_by": "E008",
              "status": "Approved",
              "submitted_at": "2023-04-05T11:00:00Z"
            }
          ],
          "end_datetime": "2023-04-05T12:00:00Z",
          "start_datetime": "2023-04-05T10:00:00Z",
          "status": "Approved"
        },
        {
          "description": {
            "date": "2023-04-12",
            "text": "New security software installed"
          },
          "drafts": [
            {
              "comments": [
                {
                  "comment": "Installation completed successfully. Proceed with testing.",
                  "comment_id": "comment_008",
                  "commented_at": "2023-04-12T13:00:00Z",
                  "commenter_id": "E008"
                }
              ],
              "content": "Installation report for security software.",
              "draft_id": "draft_008",
              "reviewed_at": "2023-04-12T13:00:00Z",
              "reviewed_by": "E008",
              "status": "Approved",
              "submitted_at": "2023-04-12T12:00:00Z"
            }
          ],
          "end_datetime": "2023-04-12T14:00:00Z",
          "start_datetime": "2023-04-12T10:00:00Z",
          "status": "Approved"
        }
      ],
      "recommendations": {
        "E006": 0.43,
        "E008": 0.56,
        "E009": 0.53
      },
      "request_type": "assigned",
      "requester_id": "E01",
      "status": "In Progress",
      "tags": [
        "security",
        "network",
        "upgrade"
      ],
      "task_assignee": "E004",
      "task_description": "Upgrade the security protocols for our network infrastructure.",
      "task_id": "T004",
      "task_title": "Security System Upgrade"
    },
    {
      "_id": "6824c957f7243b0eef118ad0",
      "approval_status": "pending",
      "assignment_date": "Wed, 14 May 2025 16:48:23 GMT",
      "associated_objective": "65f1ad4198b23e2c38c77185",
      "completion_date": null,
      "created_at": "Wed, 14 May 2025 16:48:23 GMT",
      "expected_completion_date": "2025-07-01",
      "progress": [
        {
          "description": {
            "date": "2025-05-26",
            "text": "submit a proposal with a minimum of 3000 words"
          },
          "drafts": [],
          "end_datetime": "",
          "start_datetime": "",
          "status": "Not Started"
        },
        {
          "description": {
            "date": "2025-06-16",
            "text": "Demo new process software"
          },
          "drafts": [],
          "end_datetime": "",
          "start_datetime": "",
          "status": "Not Started"
        },
        {
          "description": {
            "date": "2025-06-23",
            "text": "Refine new process with feedback and show final software"
          },
          "drafts": [],
          "end_datetime": "",
          "start_datetime": "",
          "status": "Not Started"
        }
      ],
      "recommendations": {
        "E001": 0.75
      },
      "request_type": "self-generated",
      "requester_id": "E004",
      "status": "draft",
      "tags": [
        "crm",
        "software upgrade",
        "technical committee",
        "process development",
        "application review",
        "speed",
        "transparency"
      ],
      "task_assignee": "E004",
      "task_description": "Develop a robust technical committee process that prioritizes speed and transparency in reviewing applications",
      "task_id": "4ce4cb8a",
      "task_title": "Update TCM process",
      "team_members": [
        "E004"
      ]
    }
  ]
};

initFlaskCalendarHeatmap(data);



// --- Calendar Heatmap ---
function initFlaskCalendarHeatmap(flaskData) {
    if (!window.calendarHeatmap) return;
    console.log('found window')
    const calendarContainer = document.getElementById('calendar');
    console.log('found id')
    if (!calendarContainer) return;
    calendarContainer.innerHTML = '';
    const heatmapData = generateHeatmapData(flaskData);
    if (!heatmapData.length) return;
    calendarHeatmap.init(heatmapData, calendarContainer);
}




function generateHeatmapData(flaskData) {
    const groupedData = (flaskData.heatmap_data || []).reduce((acc, entry) => {
        const dateKey = entry.date.split('T')[0];
        if (!acc[dateKey]) {
            acc[dateKey] = { date: new Date(dateKey), details: [], summary: [] };
        }
        acc[dateKey].details.push({ name: entry.task_title, value: entry.value, description: entry.description });
        acc[dateKey].summary = acc[dateKey].details;
        return acc;
    }, {});
    console.log('Grouped Data:', groupedData);
    return Object.values(groupedData).map(entry => ({...entry, init: function() { this.total = this.details.reduce((a, d) => a + d.value, 0); return this; }}).init());
}
  </script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.18.1/moment.min.js" charset="utf-8"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3/4.10.2/d3.min.js" charset="utf-8"></script>
  <script src="/static/assets/cal/dist/calendar-heatmap.min.js"></script>
</body>
</html>