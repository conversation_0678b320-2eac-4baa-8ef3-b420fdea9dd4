<!DOCTYPE html>
<html>
<head>
  <title>Radar Chart for Skills</title>
  <script src="https://d3js.org/d3.v7.min.js"></script>
</head>
<body>
  <div id="calendar" width="500" height="500"></div>
  <script>
/**
 * Staff Contribution Calendar Heatmap Generator
 * Creates a GitHub-style calendar heatmap for visualizing staff contributions over time
 */


function initFlaskCalendarHeatmap(containerId, taskData, options = {}) {

    // Default options

    const defaults = {

        colorRange: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127'],

        tooltip: (date, value) => `${date.toLocaleDateString()} - ${value.toFixed(1)} hours`,

        initialPeriod: 'year',

        initialDate: new Date()

    };

    const config = { ...defaults, ...options };



    // State variables

    let currentPeriod = config.initialPeriod;

    let currentDate = new Date(config.initialDate);

    let currentViewData = [];

    let allProcessedData = []; // Store all processed data to avoid reprocessing



    // Get the container elements

    const container = document.getElementById(containerId);

    if (!container) {

        console.error(`Container element with ID ${containerId} not found`);

        return;

    }



    // Clear previous content

    container.innerHTML = '';



    // Create the header with controls

    const headerDiv = document.createElement('div');

    headerDiv.className = 'vis-header';

    headerDiv.innerHTML = `

        <h3>Work Duration Heatmap</h3>

        <div id="heatmap-controls" class="vis-controls">

            <button class="control-btn" data-period="year">Year</button>

            <button class="control-btn" data-period="month">Month</button>

            <button class="control-btn" data-period="week">Week</button>

            <button class="control-btn" data-period="day">Day</button>

        </div>

    `;

    container.appendChild(headerDiv);



    // Create the heatmap container

    const heatmapContainer = document.createElement('div');

    heatmapContainer.id = 'calendar-heatmap-container';

    heatmapContainer.style.marginTop = '10px';

    container.appendChild(heatmapContainer);



    // Create navigation controls

    const navControls = document.createElement('div');

    navControls.id = 'navigation-controls';

    navControls.style.display = 'none';

    navControls.style.textAlign = 'center';

    navControls.style.marginTop = '1rem';

    navControls.innerHTML = `

        <button id="prev-btn">Previous</button>

        <span id="nav-title" style="margin: 0 1rem; font-weight: bold;"></span>

        <button id="next-btn">Next</button>

    `;

    container.appendChild(navControls);



    // Get references to the control elements

    const periodButtons = headerDiv.querySelectorAll('.control-btn');

    const prevBtn = navControls.querySelector('#prev-btn');

    const nextBtn = navControls.querySelector('#next-btn');

    const navTitle = navControls.querySelector('#nav-title');



    // Set up event listeners for period controls

    periodButtons.forEach(button => {

        button.addEventListener('click', () => {

            // Remove active class from all buttons

            periodButtons.forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button

            button.classList.add('active');

            // Update current period

            currentPeriod = button.getAttribute('data-period');

            // Reset date to today when changing views (optional)

            // currentDate = new Date();

            // Update the view

            updateView();

        });



        // Set initial active button

        if (button.getAttribute('data-period') === currentPeriod) {

            button.classList.add('active');

        }

    });



    // Set up event listeners for navigation controls

    prevBtn.addEventListener('click', () => {

        navigate(-1);

    });



    nextBtn.addEventListener('click', () => {

        navigate(1);

    });



    // Function to navigate between periods

    function navigate(direction) {

        switch (currentPeriod) {

            case 'year':

                currentDate.setFullYear(currentDate.getFullYear() + direction);

                break;

            case 'month':

                currentDate.setMonth(currentDate.getMonth() + direction);

                break;

            case 'week':

                currentDate.setDate(currentDate.getDate() + direction * 7);

                break;

            case 'day':

                currentDate.setDate(currentDate.getDate() + direction);

                break;

        }

        updateView();

    }



    // Function to update the view based on current period and date

    function updateView() {

        // Filter data based on current period and date

        filterDataForView();



        // Update navigation title

        updateNavTitle();



        // Render the appropriate view

        switch (currentPeriod) {

            case 'year':

                renderYearView();

                break;

            case 'month':

                renderMonthView();

                break;

            case 'week':

                renderWeekView();

                break;

            case 'day':

                renderDayView();

                break;

        }

    }



    // Function to process all data once (on initialization)

    function processAllData() {

        const dateValues = {};

        const dateFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };



        // First pass: group by date and sum total hours

        taskData.forEach(task => {

            if (task.date) {

                const date = new Date(task.date);

                const dateStr = date.toLocaleDateString('en-US', dateFormatOptions);



                if (!dateValues[dateStr]) {

                    dateValues[dateStr] = {

                        date: date,

                        dateStr: dateStr,

                        totalHours: 0,

                        sessions: []

                    };

                }

                dateValues[dateStr].totalHours += task.value || 0;

                dateValues[dateStr].sessions.push(task);

            }

        });



        // Convert to array and sort by date

        allProcessedData = Object.values(dateValues).sort((a, b) => a.date - b.date);

    }



    // Function to filter data based on current period and date

    function filterDataForView() {

        currentViewData = [];



        // For day view, we need the original sessions with timestamps

        if (currentPeriod === 'day') {

            const dayStr = currentDate.toLocaleDateString('en-US', {

                year: 'numeric',

                month: 'short',

                day: 'numeric'

            });



            const dayData = allProcessedData.find(d => d.dateStr === dayStr);

            currentViewData = dayData ? dayData.sessions : [];

        } else {

            // For other views, we use the aggregated daily data

            switch (currentPeriod) {

                case 'year':

                    const year = currentDate.getFullYear();

                    currentViewData = allProcessedData.filter(day =>

                        day.date.getFullYear() === year

                    );

                    break;

                case 'month':

                    const month = currentDate.getMonth();

                    const monthYear = currentDate.getFullYear();

                    currentViewData = allProcessedData.filter(day =>

                        day.date.getMonth() === month && day.date.getFullYear() === monthYear

                    );

                    break;

                case 'week':

                    // Get the start of the current week (Sunday)

                    const weekStart = new Date(currentDate);

                    weekStart.setDate(weekStart.getDate() - weekStart.getDay());



                    // Get the end of the current week (Saturday)

                    const weekEnd = new Date(weekStart);

                    weekEnd.setDate(weekEnd.getDate() + 6);



                    // Filter days within this week

                    currentViewData = allProcessedData.filter(day =>

                        day.date >= weekStart && day.date <= weekEnd

                    );



                    // If no data for this week, we still want to show the week structure

                    if (currentViewData.length === 0) {

                        // Create empty day objects for each day of the week

                        for (let i = 0; i < 7; i++) {

                            const day = new Date(weekStart);

                            day.setDate(weekStart.getDate() + i);

                            const dateStr = day.toLocaleDateString('en-US', {

                                year: 'numeric',

                                month: 'short',

                                day: 'numeric'

                            });



                            currentViewData.push({

                                date: day,

                                dateStr: dateStr,

                                totalHours: 0,

                                sessions: []

                            });

                        }

                    }

                    break;

            }

        }

    }



    // Function to update the navigation title

    function updateNavTitle() {

        let title = '';

        switch (currentPeriod) {

            case 'year':

                title = currentDate.getFullYear().toString();

                break;

            case 'month':

                title = currentDate.toLocaleString('default', { month: 'long', year: 'numeric' });

                break;

            case 'week':

                // Get the start of the current week (Sunday)

                const weekStart = new Date(currentDate);

                weekStart.setDate(weekStart.getDate() - weekStart.getDay());



                // Get the end of the current week (Saturday)

                const weekEnd = new Date(weekStart);

                weekEnd.setDate(weekEnd.getDate() + 6);



                // Format as "Jun 7 - Jun 13, 2023"

                const options = { month: 'short', day: 'numeric', year: 'numeric' };

                title = `${weekStart.toLocaleDateString('en-US', options)} - ${weekEnd.toLocaleDateString('en-US', options)}`;

                break;

            case 'day':

                title = currentDate.toLocaleDateString('en-US', {

                    weekday: 'long',

                    year: 'numeric',

                    month: 'long',

                    day: 'numeric'

                });

                break;

        }

        navTitle.textContent = title;

    }



    // Helper function to get color based on hours

    function getColorForHours(hours, maxHours) {

        if (hours === 0) return config.colorRange[0];

        const normalized = Math.min(hours / maxHours, 1);

        const colorIndex = Math.max(1, Math.floor(normalized * (config.colorRange.length - 1)));

        return config.colorRange[colorIndex];

    }



    // Function to render year view

    function renderYearView() {

        heatmapContainer.innerHTML = '';

        const year = currentDate.getFullYear();



        // Create SVG element

        const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");

        svg.setAttribute("width", "100%");

        svg.setAttribute("height", "auto");

        svg.setAttribute("viewBox", "0 0 800 120");

        svg.setAttribute("preserveAspectRatio", "xMidYMid meet");

        svg.style.overflow = "visible";

        heatmapContainer.appendChild(svg);



        // Get all days in the year

        const daysInYear = [];

        const firstDayOfYear = new Date(year, 0, 1);

        const lastDayOfYear = new Date(year, 11, 31);

        let currentDateIter = new Date(firstDayOfYear);



        // Create a map of date strings to our data for quick lookup

        const dateDataMap = {};

        currentViewData.forEach(dayData => {

            dateDataMap[dayData.dateStr] = dayData.totalHours;

        });



        while (currentDateIter <= lastDayOfYear) {

            const dateStr = currentDateIter.toLocaleDateString('en-US', {

                year: 'numeric',

                month: 'short',

                day: 'numeric'

            });

            const totalHours = dateDataMap[dateStr] || 0;

            daysInYear.push({

                date: new Date(currentDateIter),

                dateStr: dateStr,

                totalHours: totalHours

            });

            currentDateIter.setDate(currentDateIter.getDate() + 1);

        }



        // Group days by week for layout

        const weeks = [];

        let currentWeek = [];

        daysInYear.forEach(day => {

            if (currentWeek.length === 7) {

                weeks.push(currentWeek);

                currentWeek = [];

            }

            currentWeek.push(day);

        });

        if (currentWeek.length > 0) {

            weeks.push(currentWeek);

        }



        // Determine months to show (all 12 for year view)

        const monthsShown = 12;

        const months = [

            'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',

            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'

        ];



        // Add month labels at the top

        const monthLabelY = 20;

        let monthLabelX = 100; // Start X position to leave room for day labels

        const cellSize = 12;

        const cellSpacing = 2;

        const fullCellSize = cellSize + cellSpacing;



        for (let month = 0; month < monthsShown; month++) {

            const label = document.createElementNS("http://www.w3.org/2000/svg", "text");

            label.setAttribute("x", monthLabelX + (month * (fullCellSize * 7 + cellSpacing * 2)));

            label.setAttribute("y", monthLabelY - 5);

            label.setAttribute("text-anchor", "middle");

            label.setAttribute("fill", "#6a737d");

            label.setAttribute("font-size", "10");

            label.textContent = months[month];

            svg.appendChild(label);

        }



        // Add day labels on the left

        const dayLabels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

        const dayLabelX = 80;

        const startY = 20;



        dayLabels.forEach((day, index) => {

            const label = document.createElementNS("http://www.w3.org/2000/svg", "text");

            label.setAttribute("x", dayLabelX);

            label.setAttribute("y", startY + (index * fullCellSize) + cellSize / 2 + 5);

            label.setAttribute("text-anchor", "end");

            label.setAttribute("fill", "#6a737d");

            label.setAttribute("font-size", "10");

            label.textContent = day;

            svg.appendChild(label);

        });



        // Find the maximum hours for coloring

        const maxHours = Math.max(...daysInYear.map(day => day.totalHours), 1);



        // Create tooltip element

        const tooltip = document.createElement('div');

        tooltip.style.position = 'absolute';

        tooltip.style.backgroundColor = '#333';

        tooltip.style.color = '#fff';

        tooltip.style.padding = '5px 10px';

        tooltip.style.borderRadius = '4px';

        tooltip.style.fontSize = '12px';

        tooltip.style.pointerEvents = 'none';

        tooltip.style.opacity = '0';

        tooltip.style.transition = 'opacity 0.2s';

        tooltip.style.zIndex = '1000';

        tooltip.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';

        heatmapContainer.appendChild(tooltip);



        function showTooltip(x, y, text) {

            tooltip.textContent = text;

            tooltip.style.left = `${x + 10}px`;

            tooltip.style.top = `${y + 10}px`;

            tooltip.style.opacity = '1';

        }



        function hideTooltip() {

            tooltip.style.opacity = '0';

        }



        // Draw the calendar heatmap

        weeks.forEach((week, weekIndex) => {

            week.forEach((day, dayIndex) => {

                const month = day.date.getMonth();

                if (month >= monthsShown) return;



                const x = 100 + (weekIndex * fullCellSize) + (month * (fullCellSize * 7 + cellSpacing * 2));

                const y = startY + (dayIndex * fullCellSize);



                const rect = document.createElementNS("http://www.w3.org/2000/svg", "rect");

                rect.setAttribute("x", x);

                rect.setAttribute("y", y);

                rect.setAttribute("width", cellSize);

                rect.setAttribute("height", cellSize);

                rect.setAttribute("rx", "2");

                rect.setAttribute("ry", "2");

                rect.setAttribute("fill", getColorForHours(day.totalHours, maxHours));

                rect.setAttribute("stroke", "#fff");

                rect.setAttribute("stroke-width", "0.5");



                // Add hover effects

                rect.addEventListener('mouseenter', (e) => {

                    const rect = e.target;

                    rect.setAttribute('stroke-width', '1.5');

                    rect.setAttribute('stroke', '#000');



                    const tooltipText = config.tooltip(day.date, day.totalHours);

                    const rectPos = rect.getBoundingClientRect();

                    showTooltip(rectPos.x, rectPos.y, tooltipText);



                    rect.setAttribute('data-original-stroke-width', '0.5');

                    rect.setAttribute('data-original-stroke', '#fff');

                });



                rect.addEventListener('mouseleave', (e) => {

                    const rect = e.target;

                    rect.setAttribute('stroke-width', rect.getAttribute('data-original-stroke-width') || '0.5');

                    rect.setAttribute('stroke', rect.getAttribute('data-original-stroke') || '#fff');

                    hideTooltip();

                });



                svg.appendChild(rect);

            });

        });



        // Add legend for the color scale

        const legend = document.createElement('div');

        legend.style.display = 'flex';

        legend.style.justifyContent = 'flex-end';

        legend.style.marginTop = '10px';

        legend.style.gap = '5px';

        legend.style.alignItems = 'center';



        const legendTitle = document.createElement('span');

        legendTitle.textContent = 'Hours: ';

        legendTitle.style.marginRight = '5px';

        legendTitle.style.fontSize = '12px';

        legendTitle.style.color = '#6a737d';

        legend.appendChild(legendTitle);



        config.colorRange.slice(1).reverse().forEach((color, index) => {

            const colorBox = document.createElement('div');

            colorBox.style.width = '12px';

            colorBox.style.height = '12px';

            colorBox.style.backgroundColor = color;

            colorBox.style.borderRadius = '2px';



            const threshold = Math.round(maxHours * (index / (config.colorRange.length - 2)));

            const colorLabel = document.createElement('span');

            colorLabel.textContent = index === 0 ? '0' : `≥${threshold}`;

            colorLabel.style.fontSize = '12px';

            colorLabel.style.color = '#6a737d';

            colorLabel.style.marginRight = '5px';



            legend.appendChild(colorBox);

            if (index === config.colorRange.length - 2) {

                legend.appendChild(colorLabel);

            }

        });



        heatmapContainer.appendChild(legend);



        // Add description

        const description = document.createElement('div');

        description.style.textAlign = 'center';

        description.style.marginTop = '15px';

        description.style.color = '#6a737d';

        description.style.fontSize = '12px';

        description.innerHTML = `Work duration calendar for ${year}.<br>Darker color indicates more hours worked in a day.`;

        heatmapContainer.appendChild(description);



        // Show navigation controls

        navControls.style.display = 'flex';

    }



    // Function to render month view

    function renderMonthView() {

        heatmapContainer.innerHTML = '';



        const month = currentDate.getMonth();

        const year = currentDate.getFullYear();

        const monthName = currentDate.toLocaleString('default', { month: 'long' });



        // Create SVG element for the month view

        const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");

        svg.setAttribute("width", "100%");

        svg.setAttribute("height", "auto");

        svg.setAttribute("viewBox", "0 0 500 100");

        svg.setAttribute("preserveAspectRatio", "xMidYMid meet");

        svg.style.overflow = "visible";

        heatmapContainer.appendChild(svg);



        // Get all days in the month

        const daysInMonth = [];

        const firstDayOfMonth = new Date(year, month, 1);

        const lastDayOfMonth = new Date(year, month + 1, 0);



        let currentDateIter = new Date(firstDayOfMonth);



        // Create a map of date strings to our data for quick lookup

        const dateDataMap = {};

        currentViewData.forEach(dayData => {

            dateDataMap[dayData.dateStr] = dayData.totalHours;

        });



        while (currentDateIter <= lastDayOfMonth) {

            const dateStr = currentDateIter.toLocaleDateString('en-US', {

                year: 'numeric',

                month: 'short',

                day: 'numeric'

            });

            const totalHours = dateDataMap[dateStr] || 0;

            daysInMonth.push({

                date: new Date(currentDateIter),

                dateStr: dateStr,

                totalHours: totalHours

            });

            currentDateIter.setDate(currentDateIter.getDate() + 1);

        }



        // Group days by week for layout

        const weeks = [];

        let currentWeek = [];

        // Add empty cells for days before the first day of the month

        const firstDayOfWeek = firstDayOfMonth.getDay(); // 0 (Sunday) to 6 (Saturday)

        for (let i = 0; i < firstDayOfWeek; i++) {

            currentWeek.push(null);

        }



        // Add days of the month

        daysInMonth.forEach(day => {

            currentWeek.push(day);

            if (currentWeek.length === 7) {

                weeks.push(currentWeek);

                currentWeek = [];

            }

        });



        // Add empty cells for days after the last day of the month

        if (currentWeek.length > 0) {

            while (currentWeek.length < 7) {

                currentWeek.push(null);

            }

            weeks.push(currentWeek);

        }



        // Calculate cell size and spacing

        const cellSize = 20;

        const cellSpacing = 2;

        const fullCellSize = cellSize + cellSpacing;

        const startX = 50; // Start X position

        const startY = 30; // Start Y position



        // Add month name

        const monthTitle = document.createElementNS("http://www.w3.org/2000/svg", "text");

        monthTitle.setAttribute("x", "50%");

        monthTitle.setAttribute("y", "20");

        monthTitle.setAttribute("text-anchor", "middle");

        monthTitle.setAttribute("fill", "#333");

        monthTitle.setAttribute("font-size", "14");

        monthTitle.setAttribute("font-weight", "bold");

        monthTitle.textContent = `${monthName} ${year}`;

        svg.appendChild(monthTitle);



        // Add day labels

        const dayLabels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

        dayLabels.forEach((day, index) => {

            const label = document.createElementNS("http://www.w3.org/2000/svg", "text");

            label.setAttribute("x", startX + index * fullCellSize + cellSize / 2);

            label.setAttribute("y", startY - 10);

            label.setAttribute("text-anchor", "middle");

            label.setAttribute("fill", "#6a737d");

            label.setAttribute("font-size", "10");

            label.textContent = day;

            svg.appendChild(label);

        });



        // Find the maximum hours for coloring

        const maxHours = Math.max(...daysInMonth.map(day => day.totalHours), 1);



        // Create tooltip element

        const tooltip = document.createElement('div');

        tooltip.style.position = 'absolute';

        tooltip.style.backgroundColor = '#333';

        tooltip.style.color = '#fff';

        tooltip.style.padding = '5px 10px';

        tooltip.style.borderRadius = '4px';

        tooltip.style.fontSize = '12px';

        tooltip.style.pointerEvents = 'none';

        tooltip.style.opacity = '0';

        tooltip.style.transition = 'opacity 0.2s';

        tooltip.style.zIndex = '1000';

        tooltip.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';

        heatmapContainer.appendChild(tooltip);



        function showTooltip(x, y, text) {

            tooltip.textContent = text;

            tooltip.style.left = `${x + 10}px`;

            tooltip.style.top = `${y + 10}px`;

            tooltip.style.opacity = '1';

        }



        function hideTooltip() {

            tooltip.style.opacity = '0';

        }



        // Draw the calendar heatmap

        weeks.forEach((week, weekIndex) => {

            week.forEach((day, dayIndex) => {

                const x = startX + (dayIndex * fullCellSize);

                const y = startY + (weekIndex * fullCellSize);



                if (day) {

                    const rect = document.createElementNS("http://www.w3.org/2000/svg", "rect");

                    rect.setAttribute("x", x);

                    rect.setAttribute("y", y);

                    rect.setAttribute("width", cellSize);

                    rect.setAttribute("height", cellSize);

                    rect.setAttribute("rx", "2");

                    rect.setAttribute("ry", "2");

                    rect.setAttribute("fill", getColorForHours(day.totalHours, maxHours));

                    rect.setAttribute("stroke", "#fff");

                    rect.setAttribute("stroke-width", "0.5");



                    // Add date text

                    const dateText = document.createElementNS("http://www.w3.org/2000/svg", "text");

                    dateText.setAttribute("x", x + cellSize / 2);

                    dateText.setAttribute("y", y + cellSize / 2 + 4);

                    dateText.setAttribute("text-anchor", "middle");

                    dateText.setAttribute("fill", day.totalHours > 0 ? "#fff" : "#6a737d");

                    dateText.setAttribute("font-size", "8");

                    dateText.textContent = day.date.getDate();

                    svg.appendChild(dateText);



                    // Add hover effects

                    rect.addEventListener('mouseenter', (e) => {

                        const rect = e.target;

                        rect.setAttribute('stroke-width', '1.5');

                        rect.setAttribute('stroke', '#000');



                        const tooltipText = config.tooltip(day.date, day.totalHours);

                        const rectPos = rect.getBoundingClientRect();

                        showTooltip(rectPos.x, rectPos.y, tooltipText);



                        rect.setAttribute('data-original-stroke-width', '0.5');

                        rect.setAttribute('data-original-stroke', '#fff');

                    });



                    rect.addEventListener('mouseleave', (e) => {

                        const rect = e.target;

                        rect.setAttribute('stroke-width', rect.getAttribute('data-original-stroke-width') || '0.5');

                        rect.setAttribute('stroke', rect.getAttribute('data-original-stroke') || '#fff');

                        hideTooltip();

                    });



                    svg.appendChild(rect);

                }

            });

        });



        // Add legend for the color scale

        const legend = document.createElement('div');

        legend.style.display = 'flex';

        legend.style.justifyContent = 'flex-end';

        legend.style.marginTop = '10px';

        legend.style.gap = '5px';

        legend.style.alignItems = 'center';



        const legendTitle = document.createElement('span');

        legendTitle.textContent = 'Hours: ';

        legendTitle.style.marginRight = '5px';

        legendTitle.style.fontSize = '12px';

        legendTitle.style.color = '#6a737d';

        legend.appendChild(legendTitle);



        config.colorRange.slice(1).reverse().forEach((color, index) => {

            const colorBox = document.createElement('div');

            colorBox.style.width = '12px';

            colorBox.style.height = '12px';

            colorBox.style.backgroundColor = color;

            colorBox.style.borderRadius = '2px';



            const threshold = Math.round(maxHours * (index / (config.colorRange.length - 2)));

            const colorLabel = document.createElement('span');

            colorLabel.textContent = index === 0 ? '0' : `≥${threshold}`;

            colorLabel.style.fontSize = '12px';

            colorLabel.style.color = '#6a737d';

            colorLabel.style.marginRight = '5px';



            legend.appendChild(colorBox);

            if (index === config.colorRange.length - 2) {

                legend.appendChild(colorLabel);

            }

        });



        heatmapContainer.appendChild(legend);



        // Add description

        const description = document.createElement('div');

        description.style.textAlign = 'center';

        description.style.marginTop = '15px';

        description.style.color = '#6a737d';

        description.style.fontSize = '12px';

        description.innerHTML = `Work duration calendar for ${monthName} ${year}.<br>Darker color indicates more hours worked in a day.`;

        heatmapContainer.appendChild(description);



        navControls.style.display = 'flex';

    }



    function renderDayView() {

    heatmapContainer.innerHTML = '';



    const dayDate = currentDate.toLocaleDateString('en-US', {

        weekday: 'long',

        year: 'numeric',

        month: 'long',

        day: 'numeric'

    });



    // Create a title

    const title = document.createElement('h4');

    title.textContent = `Day: ${dayDate}`;

    title.style.textAlign = 'center';

    title.style.marginBottom = '10px';

    heatmapContainer.appendChild(title);



    // Check if we have data for this day

    const dayStr = currentDate.toLocaleDateString('en-US', {

        year: 'numeric',

        month: 'short',

        day: 'numeric'

    });



//     const dayData = currentViewData.find(d => d.dateStr === dayStr) || { sessions: [] };
    const dayData = allProcessedData.find(d => d.dateStr === dayStr) || { sessions: [] };



    if (dayData.sessions.length === 0) {

        const noData = document.createElement('p');

        noData.textContent = 'No work sessions recorded for this day.';

        noData.style.textAlign = 'center';

        heatmapContainer.appendChild(noData);

        navControls.style.display = 'flex';

        return;

    }



    // Create a timeline visualization

    const timeline = document.createElement('div');

    timeline.style.position = 'relative';

    timeline.style.height = '200px';

    timeline.style.borderLeft = '1px solid #ccc';

    timeline.style.margin = '20px 0';

    heatmapContainer.appendChild(timeline);



    // Add time labels

    for (let hour = 0; hour <= 24; hour += 2) {

        const timeLabel = document.createElement('div');

        timeLabel.textContent = `${hour % 24}:00`;

        timeLabel.style.position = 'absolute';

        timeLabel.style.left = '-30px';

        timeLabel.style.width = '30px';

        timeLabel.style.textAlign = 'right';

        timeLabel.style.top = `${(hour / 24) * 100}%`;

        timeLabel.style.transform = 'translateY(-50%)';

        timeLabel.style.fontSize = '12px';

        timeLabel.style.color = '#666';

        timeline.appendChild(timeLabel);

    }



    // Process sessions to visualize them

    dayData.sessions.forEach(session => {

        const startTime = new Date(session.timestamp);

        const durationHours = session.value;



        // Calculate start position (as percentage of day)

        const startHour = startTime.getHours() + (startTime.getMinutes() / 60);

        const startPercent = (startHour / 24) * 100;



        // Calculate height (as percentage of day)

        const heightPercent = (durationHours / 24) * 100;



        // Create session block

        const sessionBlock = document.createElement('div');

        sessionBlock.style.position = 'absolute';

        sessionBlock.style.left = '0';

        sessionBlock.style.width = '100%';

        sessionBlock.style.top = `${startPercent}%`;

        sessionBlock.style.height = `${heightPercent}%`;

        sessionBlock.style.backgroundColor = getColorForHours(durationHours);

        sessionBlock.style.borderRadius = '3px';

        sessionBlock.style.marginLeft = '10px';

        sessionBlock.style.cursor = 'pointer';



        // Add tooltip

        sessionBlock.title = `${session.task_title}\n` +

                           `${startTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })} - ` +

                           `${durationHours.toFixed(1)} hours\n` +

                           `${session.description}`;



        // Add to timeline

        timeline.appendChild(sessionBlock);

    });



    // Add legend

    const maxHours = Math.max(...dayData.sessions.map(s => s.value), 1);

    function getColorForHours(hours) {

        if (hours === 0) return config.colorRange[0];

        const normalized = Math.min(hours / maxHours, 1);

        const colorIndex = Math.max(1, Math.floor(normalized * (config.colorRange.length - 1)));

        return config.colorRange[colorIndex];

    }



    const legend = document.createElement('div');

    legend.style.display = 'flex';

    legend.style.justifyContent = 'flex-end';

    legend.style.marginTop = '10px';

    legend.style.gap = '5px';

    legend.style.alignItems = 'center';



    const legendTitle = document.createElement('span');

    legendTitle.textContent = 'Hours: ';

    legendTitle.style.marginRight = '5px';

    legendTitle.style.fontSize = '12px';

    legendTitle.style.color = '#6a737d';

    legend.appendChild(legendTitle);



    config.colorRange.slice(1).reverse().forEach((color, index) => {

        const colorBox = document.createElement('div');

        colorBox.style.width = '12px';

        colorBox.style.height = '12px';

        colorBox.style.backgroundColor = color;

        colorBox.style.borderRadius = '2px';



        const threshold = Math.round(maxHours * (index / (config.colorRange.length - 2)));

        const colorLabel = document.createElement('span');

        colorLabel.textContent = index === 0 ? '0' : `≥${threshold}`;

        colorLabel.style.fontSize = '12px';

        colorLabel.style.color = '#6a737d';

        colorLabel.style.marginRight = '5px';



        legend.appendChild(colorBox);

        if (index === config.colorRange.length - 2) {

            legend.appendChild(colorLabel);

        }

    });



    heatmapContainer.appendChild(legend);



    // Add description

    const description = document.createElement('div');

    description.style.textAlign = 'center';

    description.style.marginTop = '15px';

    description.style.color = '#6a737d';

    description.style.fontSize = '12px';

    description.innerHTML = `Day view showing work sessions.<br>Each block represents a work session with duration in hours.`;

    heatmapContainer.appendChild(description);



    navControls.style.display = 'flex';

}
}


staffData = [
    { "date": "2025-07-01", "timestamp": "2025-07-01T09:00:00", "value": 1.5, "task_title": "Design Mockups", "description": "Initial design phase" },
    { "date": "2025-07-01", "timestamp": "2025-07-01T14:30:00", "value": 2.0, "task_title": "Frontend Development", "description": "Implement the new UI" },
    { "date": "2025-06-30", "timestamp": "2025-06-30T11:00:00", "value": 3.0, "task_title": "API Integration", "description": "Connect to the user service" }
    // ... more task data
];
initFlaskCalendarHeatmap('calendar', staffData);

  </script>
</body>
</html>
