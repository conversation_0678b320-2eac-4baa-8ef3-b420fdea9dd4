<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Radar Chart</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/4.10.2/d3.min.js" charset="utf-8"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .radar-chart {
            width: 600px;
            height: 600px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="radar-chart"></div>
<script>

// Sample data
const data = {
    skills: [
        { category: "soft", skill: "Project Management", count: 1 },
        { category: "hard", skill: "Network Design", count: 1 },
        { category: "hard", skill: "Surveying", count: 1 },
        { category: "hard", skill: "Procurement", count: 1 },
        { category: "hard", skill: "Construction Management", count: 1 },
        { category: "hard", skill: "Network Testing", count: 1 }
    ]
};

// Define the dimensions and scales
const width = 600;
const height = 600;
const margin = { top: 100, right: 100, bottom: 100, left: 100 };

const radarChart = d3.select('.radar-chart')
    .append('svg')
    .attr('width', width)
    .attr('height', height)
    .append('g')
    .attr('transform', `translate(${width / 2}, ${height / 2})`);

// Create the radar chart
const levels = 5;
const maxValue = 1;
const radius = Math.min(width, height) / 2 - Math.max(margin.top, margin.right, margin.bottom, margin.left);

const angleSlice = Math.PI * 2 / data.skills.length;

const rScale = d3.scaleLinear()
    .range([0, radius])
    .domain([0, maxValue]);

// Draw the circular grid
for (let level = 0; level < levels; level++) {
    const levelFactor = radius * ((level + 1) / levels);

    radarChart.selectAll('.levels')
        .data([1])
        .enter()
        .append('circle')
        .attr('class', 'grid-circle')
        .attr('r', levelFactor)
        .style('fill', 'none')
        .style('stroke', '#ccc')
        .style('stroke-width', '0.5px');
}

// Draw the axes
const axis = radarChart.selectAll('.axis')
    .data(data.skills)
    .enter()
    .append('g')
    .attr('class', 'axis');

axis.append('line')
    .attr('x1', 0)
    .attr('y1', 0)
    .attr('x2', (d, i) => rScale(maxValue) * Math.sin(i * angleSlice))
    .attr('y2', (d, i) => -rScale(maxValue) * Math.cos(i * angleSlice))
    .attr('class', 'line')
    .style('stroke', '#ccc')
    .style('stroke-width', '1px');

axis.append('text')
    .attr('class', 'legend')
    .style('font-size', '11px')
    .attr('text-anchor', 'middle')
    .attr('dy', '0.35em')
    .attr('x', (d, i) => (rScale(maxValue) + 10) * Math.sin(i * angleSlice))
    .attr('y', (d, i) => - (rScale(maxValue) + 10) * Math.cos(i * angleSlice))
    .text(d => d.skill);

// Draw the radar area
const radarLine = d3.lineRadial()
    // --- THIS IS THE CHANGE ---
    // Replaced d3.curveLinearClosed with d3.curveCardinalClosed for smooth lines
    .curve(d3.curveCardinalClosed)
    // --------------------------
    .radius(d => rScale(d.count))
    .angle((d, i) => i * angleSlice);

const color = d3.scaleOrdinal()
    .domain(['soft', 'hard'])
    .range(['#FFD700', '#FF6347']);

data.skills.forEach(skill => {
    radarChart.append('path')
        .datum(data.skills)
        .attr('class', 'radar-area')
        .attr('d', radarLine)
        .style('fill', color(skill.category))
        .style('fill-opacity', 0.2)
        .style('stroke', color(skill.category))
        .style('stroke-width', '2px');
});

// Add static tooltip
radarChart.selectAll('.dot')
    .data(data.skills)
    .enter()
    .append('circle')
    .attr('class', 'dot')
    .attr('cx', (d, i) => rScale(d.count) * Math.sin(i * angleSlice))
    .attr('cy', (d, i) => -rScale(d.count) * Math.cos(i * angleSlice))
    .attr('r', 4)
    .style('fill', d => color(d.category))
    .on('mouseover', function(d) {
        const tooltip = d3.select('body').append('div')
            .attr('class', 'tooltip')
            .style('position', 'absolute')
            .style('background', '#f4f4f4')
            .style('padding', '5px')
            .style('border-radius', '5px')
            .style('pointer-events', 'none');

        tooltip.html(`<strong>${d.skill}</strong><br/>Category: ${d.category}<br/>Count: ${d.count}`)
            .style('left', (d3.event.pageX + 10) + 'px')
            .style('top', (d3.event.pageY - 28) + 'px');
    })
    .on('mouseout', function() {
        d3.select('.tooltip').remove();
    });

</script>
</body>
</html>


