import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from datetime import datetime, timedelta
import calendar
from collections import defaultdict

def generate_staff_calendar_heatmap(staff_data, year=None, save_path=None, figsize=(15, 4)):
    """
    Generate a GitHub-style calendar heatmap for staff contributions
    
    Think of this like a digital garden - each day is a plot of soil, and the darker 
    the green, the more seeds (contributions) were planted that day!
    
    Args:
        staff_data (dict): Staff data object containing heatmap_data
        year (int, optional): Year to visualize. If None, uses current year
        save_path (str, optional): Path to save the visualization
        figsize (tuple): Figure size (width, height)
    
    Returns:
        matplotlib.figure.Figure: The generated heatmap figure
    """
    
    # Extract heatmap data - like gathering seeds from the data garden
    heatmap_data = staff_data.get('heatmap_data', [])
    staff_name = f"{staff_data.get('first_name', '')} {staff_data.get('last_name', '')}"
    
    # Determine year to visualize
    if year is None:
        year = datetime.now().year
    
    # Create a dictionary to store contribution values by date
    contributions = defaultdict(float)
    
    # Process heatmap data - like cataloging all the seeds by planting date
    for entry in heatmap_data:
        try:
            date_str = entry.get('date', '')
            value = float(entry.get('value', 0))
            
            # Parse date (handle different formats)
            try:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            except ValueError:
                try:
                    date_obj = datetime.strptime(date_str, '%m/%d/%Y')
                except ValueError:
                    continue  # Skip invalid dates
            
            # Only include data from the specified year
            if date_obj.year == year:
                date_key = date_obj.strftime('%Y-%m-%d')
                contributions[date_key] += value
                
        except (ValueError, TypeError):
            continue  # Skip invalid entries
    
    # Create date range for the entire year - like preparing the garden grid
    start_date = datetime(year, 1, 1)
    end_date = datetime(year, 12, 31)
    
    # Find the Monday of the week containing January 1st
    start_monday = start_date - timedelta(days=start_date.weekday())
    
    # Find the Sunday of the week containing December 31st
    end_sunday = end_date + timedelta(days=(6 - end_date.weekday()))
    
    # Calculate total weeks
    total_days = (end_sunday - start_monday).days + 1
    total_weeks = total_days // 7
    
    # Create matrix for heatmap (7 rows for days of week, total_weeks columns)
    heatmap_matrix = np.zeros((7, total_weeks))
    date_matrix = []
    
    # Fill the matrix - like planting seeds in each plot
    current_date = start_monday
    for week in range(total_weeks):
        week_dates = []
        for day in range(7):
            date_key = current_date.strftime('%Y-%m-%d')
            week_dates.append(current_date)
            
            # Only count contributions if the date is within the target year
            if current_date.year == year:
                heatmap_matrix[day, week] = contributions.get(date_key, 0)
            else:
                heatmap_matrix[day, week] = np.nan  # Use NaN for dates outside target year
            
            current_date += timedelta(days=1)
        date_matrix.append(week_dates)
    
    # Calculate contribution levels (like soil fertility levels)
    valid_contributions = [v for v in contributions.values() if v > 0]
    if valid_contributions:
        max_contribution = max(valid_contributions)
        # Create 5 levels: 0, low, medium-low, medium-high, high
        levels = [0, max_contribution * 0.2, max_contribution * 0.4, 
                 max_contribution * 0.7, max_contribution]
    else:
        levels = [0, 1, 2, 3, 4]  # Default levels if no contributions
    
    # Create color map - like different shades of green for plant growth
    colors = ['#ebedf0', '#9be9a8', '#40c463', '#30a14e', '#216e39']
    
    # Create the plot - time to display our garden!
    fig, ax = plt.subplots(figsize=figsize)
    
    # Draw the heatmap squares
    square_size = 0.8
    for week in range(total_weeks):
        for day in range(7):
            value = heatmap_matrix[day, week]
            
            # Determine color based on contribution level
            if np.isnan(value):
                color = '#f0f0f0'  # Light gray for dates outside target year
            elif value == 0:
                color = colors[0]  # Light gray for no contributions
            else:
                # Find appropriate color level
                level = 1
                for i, threshold in enumerate(levels[1:], 1):
                    if value <= threshold:
                        level = i
                        break
                color = colors[min(level, len(colors) - 1)]
            
            # Draw the square
            rect = patches.Rectangle((week, 6 - day), square_size, square_size,
                                   linewidth=1, edgecolor='white', facecolor=color)
            ax.add_patch(rect)
    
    # Set up the axes - like adding labels to our garden
    ax.set_xlim(0, total_weeks)
    ax.set_ylim(0, 7)
    
    # Add day labels (Mon, Wed, Fri)
    day_labels = ['Mon', '', 'Wed', '', 'Fri', '', 'Sun']
    ax.set_yticks(range(7))
    ax.set_yticklabels(reversed(day_labels), fontsize=9)
    
    # Add month labels
    month_positions = []
    month_labels = []
    current_month = start_monday.month
    
    for week in range(total_weeks):
        week_start = start_monday + timedelta(weeks=week)
        if week_start.month != current_month and week_start.year == year:
            month_positions.append(week)
            month_labels.append(calendar.month_abbr[week_start.month])
            current_month = week_start.month
    
    # Add January if we start mid-week
    if start_monday.year == year:
        month_positions.insert(0, 0)
        month_labels.insert(0, 'Jan')
    
    ax.set_xticks(month_positions)
    ax.set_xticklabels(month_labels, fontsize=9)
    
    # Style the plot
    ax.set_aspect('equal')
    ax.set_title(f'{staff_name} - Contribution Activity ({year})', 
                fontsize=14, fontweight='bold', pad=20)
    
    # Remove spines
    for spine in ax.spines.values():
        spine.set_visible(False)
    
    # Add contribution summary
    total_contributions = sum(contributions.values())
    contribution_days = len([v for v in contributions.values() if v > 0])
    
    summary_text = f'Total Contributions: {total_contributions:.1f} | Active Days: {contribution_days}'
    ax.text(0.02, 0.98, summary_text, transform=ax.transAxes, 
            fontsize=10, verticalalignment='top',
            bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgray', alpha=0.7))
    
    # Add legend - like a key for understanding soil fertility
    legend_x = total_weeks - 12
    legend_y = -0.8
    
    ax.text(legend_x - 2, legend_y, 'Less', fontsize=8, ha='right', va='center')
    
    for i, color in enumerate(colors):
        rect = patches.Rectangle((legend_x + i * 1.2, legend_y - 0.15), 
                               0.8, 0.3, facecolor=color, edgecolor='white')
        ax.add_patch(rect)
    
    ax.text(legend_x + len(colors) * 1.2, legend_y, 'More', fontsize=8, ha='left', va='center')
    
    plt.tight_layout()
    
    # Save if path provided
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Heatmap saved to: {save_path}")
    
    return fig

def analyze_contribution_patterns(staff_data, year=None):
    """
    Analyze contribution patterns from staff data
    
    Like being a garden scientist - studying growth patterns over time!
    
    Args:
        staff_data (dict): Staff data object
        year (int, optional): Year to analyze
    
    Returns:
        dict: Analysis results
    """
    heatmap_data = staff_data.get('heatmap_data', [])
    
    if year is None:
        year = datetime.now().year
    
    contributions = defaultdict(float)
    monthly_totals = defaultdict(float)
    weekday_totals = defaultdict(float)
    
    for entry in heatmap_data:
        try:
            date_str = entry.get('date', '')
            value = float(entry.get('value', 0))
            
            try:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            except ValueError:
                try:
                    date_obj = datetime.strptime(date_str, '%m/%d/%Y')
                except ValueError:
                    continue
            
            if date_obj.year == year:
                date_key = date_obj.strftime('%Y-%m-%d')
                contributions[date_key] += value
                monthly_totals[date_obj.month] += value
                weekday_totals[date_obj.strftime('%A')] += value
                
        except (ValueError, TypeError):
            continue
    
    total_contributions = sum(contributions.values())
    active_days = len([v for v in contributions.values() if v > 0])
    avg_contribution = total_contributions / max(active_days, 1)
    
    # Find most productive month
    most_productive_month = max(monthly_totals.items(), key=lambda x: x[1], default=(None, 0))
    
    # Find most productive weekday
    most_productive_weekday = max(weekday_totals.items(), key=lambda x: x[1], default=(None, 0))
    
    return {
        'total_contributions': total_contributions,
        'active_days': active_days,
        'average_contribution_per_day': avg_contribution,
        'most_productive_month': calendar.month_name[most_productive_month[0]] if most_productive_month[0] else 'None',
        'most_productive_weekday': most_productive_weekday[0] if most_productive_weekday[0] else 'None',
        'monthly_breakdown': dict(monthly_totals),
        'weekday_breakdown': dict(weekday_totals)
    }

# Example usage function
def demo_staff_heatmap():
    """
    Demo function showing how to use the calendar heatmap
    """
    # Sample staff data (like the one you provided)
    sample_staff_data = {
        "first_name": "Angela",
        "last_name": "Martinez",
        "staff_id": "E004",
        "heatmap_data": [
            {
                "date": "2023-04-05",
                "description": "Security assessment completed",
                "task_title": "Security System Upgrade",
                "value": 2.0
            },
            {
                "date": "2023-04-12",
                "description": "New security software installed",
                "task_title": "Security System Upgrade",
                "value": 4.0
            },
            # Add more sample data for better visualization
            {
                "date": "2023-04-15",
                "description": "Testing completed",
                "value": 3.0
            },
            {
                "date": "2023-05-02",
                "description": "Documentation updated",
                "value": 1.5
            },
            {
                "date": "2023-05-10",
                "description": "Project completed",
                "value": 5.0
            }
        ]
    }
    
    # Generate heatmap
    print("Generating calendar heatmap...")
    fig = generate_staff_calendar_heatmap(sample_staff_data, year=2023)
    
    # Analyze patterns
    analysis = analyze_contribution_patterns(sample_staff_data, year=2023)
    print("\nContribution Analysis:")
    print(f"Total Contributions: {analysis['total_contributions']}")
    print(f"Active Days: {analysis['active_days']}")
    print(f"Average per Day: {analysis['average_contribution_per_day']:.2f}")
    print(f"Most Productive Month: {analysis['most_productive_month']}")
    print(f"Most Productive Weekday: {analysis['most_productive_weekday']}")
    
    plt.show()
    return fig, analysis

if __name__ == "__main__":
    # Run the demo
    demo_staff_heatmap()