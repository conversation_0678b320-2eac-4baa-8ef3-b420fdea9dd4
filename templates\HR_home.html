<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HR Administration Dashboard</title>

    <!-- jQuery CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Google Fonts -->
    <link href='http://fonts.googleapis.com/css?family=Open+Sans:400,300' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Raleway' rel='stylesheet' type='text/css'>

    <!-- <link rel="stylesheet" href="/static/assets/hr_page.css"> -->
    <link rel="stylesheet" type="text/css" href="/static/assets/cal/dist/calendar-heatmap.min.css">

    <link rel="stylesheet" href="/static/assets/hr_page.css">

<style>
    body {
        font-family: 'Open Sans', sans-serif;
        background-color: #f5f5f5;
        color: #333;
        margin: 0;
        padding: 0;
    }

    header {
        background-color: #000;
        color: #fff;
        padding: 1rem;
        text-align: center;
        font-size: 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    #navigation {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 1rem;
    }

    #navigation a {
        color: #fff;
        text-decoration: none;
        font-size: 1rem;
    }

    main {
        padding: 2rem;
    }

    #button-row {
        display: flex;
        justify-content: space-around;
        margin-bottom: 2rem;
    }

    button {
        background-color: #000;
        color: #fff;
        border: none;
        padding: 1rem 2rem;
        font-size: 1rem;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    button:hover {
        background-color: #333;
    }

    .section-content {
        background-color: #fff;
        padding: 2rem;
        border: 2px solid #000;
        margin-bottom: 2rem;
    }

    form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    label {
        font-weight: bold;
    }

    input, select, textarea {
        padding: 0.5rem;
        border: 2px solid #000;
        font-size: 1rem;
    }

    hr {
        border: 2px solid #000;
        margin: 2rem 0;
    }

    /* Styles for the two-column layout in employee overview */
    #employee-skills-chart {
        display: flex;
        align-items: flex-start;
    }

    .radar-container {
        flex: 3; /* Larger column for the graph */
        margin-right: 20px; /* Space between the columns */
    }

    .radar-tooltip-container {
        flex: 1; /* Smaller column for the tooltip */
        background-color: #f9f9f9;
        padding: 10px;
        border: 1px solid #ccc;
        border-radius: 5px;
    }

    .radar-tooltip-content p {
        margin: 5px 0;
    }

    /* Styles for the new task assignment tab */
    #task-assignment-tabs {
        display: flex;
        justify-content: space-around;
        margin-bottom: 2rem;
    }

    #task-assignment-tabs button {
        background-color: #000;
        color: #fff;
        border: none;
        padding: 1rem 2rem;
        font-size: 1rem;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    #task-assignment-tabs button:hover {
        background-color: #333;
    }

    .task-content {
        display: none;
    }

    .task-content.active {
        display: block;
    }

    .recommendations {
        margin-top: 2rem;
    }

    .recommendations h3 {
        margin-bottom: 1rem;
    }

    .recommendations ul {
        list-style-type: none;
        padding: 0;
    }

    .recommendations li {
        margin-bottom: 1rem;
    }

    .recommendations li input[type="radio"] {
        margin-right: 1rem;
    }

    /* Styles for KPA section */
    .kpa-section {
        margin-top: 2rem;
    }

    .kpa-section h3 {
        margin-bottom: 1rem;
    }

    .kpa-input-group {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .kpa-input-group input[type="text"] {
        flex: 1;
        margin-right: 1rem;
    }

    .kpa-input-group input[type="date"] {
        flex: 1;
        margin-right: 1rem;
    }

    .kpa-input-group button {
        background-color: #fff;
        border: 2px solid #000;
        color: #000;
        padding: 0.5rem 1rem;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .kpa-input-group button:hover {
        background-color: #333;
        color: #fff;
    }

    /* Styles for notifications */
    .notifications-container {
        position: relative;
    }

    .notifications-dropdown {
        display: none;
        position: absolute;
        top: 100%;
        right: 0;
        background-color: #fff;
        border: 2px solid #000;
        width: 300px;
        z-index: 1000;
    }

    .notifications-dropdown.active {
        display: block;
    }

    .notification {
        border: 2px solid #000;
        padding: 1rem;
        margin: 1rem 0;
        background-color: #fff;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .notification:hover {
        background-color: #e9e9e9;
    }

    .notification p {
        margin: 0;
        font-weight: bold;
    }

    .notification small {
        color: #666;
    }

    .user-logo {
        margin-left: 1rem;
    }

    .user-logo img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
    }


/* NEW: A generic container for visualizations */
    .visualization-container {
        margin-top: 1rem;
        padding: 1rem;
        border: 1px solid #e0e0e0;
        background-color: #fdfdfd;
    }

    .visualization-container h3 {
        margin-top: 0;
        border-bottom: 2px solid #f0f0f0;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }

    



    /* Modal Styles */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .modal.active {
        display: flex;
    }

    .modal-content {
        background-color: #fff;
        padding: 2rem;
        border: 2px solid #000;
        width: 80%;
        max-width: 800px;
        overflow: auto;
        position: relative;
    }

    .close {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background-color: #000;
        color: #fff;
        border: none;
        padding: 0.5rem;
        cursor: pointer;
    }

    .close:hover {
        background-color: #333;
    }

    .draft-item {
        border-bottom: 2px solid #000;
        padding: 1rem;
        margin: 1rem 0;
    }

    .draft-review {
        margin: 1.5rem 0;
        padding: 1rem;
        background-color: #f9f9f9;
        border: 2px solid #000;
    }

    /* Error handling styles */
    .error {
        color: white;
        background-color: black;
        margin-top: 1rem;
        padding: 0.5rem;
        font-weight: bold;
        border: 2px solid red;
        text-transform: uppercase;
    }

    input.error-field, select.error-field, textarea.error-field {
        border: 2px solid red;
        background-color: #fff0f0;
    }
    
    /* REMOVED OLD TASK TRACKING STYLES */
    /* All styles for #progress, #progress-bar, and #progress-text have been removed. */

    .tasks-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;
    }

    .tasks-table th, .tasks-table td {
        border: 2px solid #000;
        padding: 0.5rem;
        text-align: left;
    }

    .tasks-table th {
        background-color: #f2f2f2;
        font-weight: bold;
    }

    .view-task-btn {
        background-color: #000;
        color: #fff;
        border: none;
        padding: 0.5rem 1rem;
        cursor: pointer;
    }

    .view-task-btn:hover {
        background-color: #333;
    }

    .approval-buttons {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .approval-buttons button {
        flex: 1;
        padding: 1rem;
        font-size: 1rem;
        cursor: pointer;
        border: 2px solid #000;
    }

    #approve-task-btn {
        background-color: #fff;
        color: #000;
    }

    #approve-task-btn:hover, #approve-task-btn.active {
        background-color: #4caf50;
        color: #fff;
    }

    #reject-task-btn {
        background-color: #fff;
        color: #000;
    }

    #reject-task-btn:hover, #reject-task-btn.active {
        background-color: #f44336;
        color: #fff;
    }

    /* D3 Tree Styles */
    .node circle {
        fill: #fff;
        stroke: steelblue;
        stroke-width: 3px;
        cursor: pointer;
    }

    .node text {
        font: 12px sans-serif;
        cursor: pointer;
        font-weight: bold;
    }

    .link {
        fill: none;
        stroke: #ccc;
        stroke-width: 2px;
    }

    .node:hover circle {
        fill: #f8f8f8;
    }
</style>






</head>
<body>
<header>
    <div id="logo">HR Logo</div>
    <nav id="navigation">
        <!-- Navigation icons -->
    </nav>
    <div class="user-logo">
        <img src="path/to/user/logo.jpg" alt="User Logo">
    </div>
    <div class="notifications-container">
        <button id="notifications-btn">Notifications</button>
        <div id="notifications-dropdown" class="notifications-dropdown">
            <div id="notifications-content">
                <!-- Notifications will be dynamically inserted here -->
            </div>
        </div>
    </div>
</header>

<main>
    <section id="button-row">
        <!-- Buttons to load different content -->
        <button data-section="user-profile">User Profile</button>
        <button data-section="organisational-objectives">Organisational Objectives</button>
        <button data-section="objectives-tree">Objectives Tree</button>
        <button data-section="employee-overview">Employee Overview</button>
        <button data-section="task-assignment">Task Assignment</button>
        <button data-section="task-tracking">Task Tracking</button>
    </section>

    <section id="content-container">
        <!-- Dynamic content will load here -->
        <div id="user-profile" class="section-content">
            <form id="create-user-form">
                <h2>Basic Information</h2>
                <label for="first-name">First Name:</label>
                <input type="text" id="first-name" name="first-name" required>
                <label for="last-name">Last Name:</label>
                <input type="text" id="last-name" name="last-name" required>
                <label for="gender">Gender:</label>
                <select id="gender" name="gender" required>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                </select>
                <label for="division">Division:</label>
                <select id="division" name="division" required>
                    <option value="Regulatory Administration">Regulatory Administration</option>
                    <option value="Engineering">Engineering</option>
                    <option value="Human Resources">Human Resources</option>
                </select>
                <label for="status">Status:</label>
                <select id="status" name="status" required>
                    <option value="Division_head">Division head</option>
                    <option value="Unit_head">Unit head</option>
                    <option value="Unit_member">Unit member</option>
                </select>
                <label for="rank">Rank:</label>
                <select id="rank" name="rank" required>
                    <option value="Director">Director</option>
                    <option value="Deputy Director">Deputy Director</option>
                    <option value="Principal Manager">Principal Manager</option>
                    <option value="Chief Manager">Chief Manager</option>
                    <option value="Senior Manager">Senior Manager</option>
                    <option value="Manager">Manager</option>
                    <option value="Deputy Manager">Deputy Manager</option>
                    <option value="Assistant Manager">Assistant Manager</option>
                    <option value="Officer">Officer</option>
                </select>
                <label for="date-of-birth">Date of Birth:</label>
                <input type="date" id="date-of-birth" name="date-of-birth" required>
                <!-- Additional fields for certifications, degrees, etc. -->
                <hr>
                <h2>Professional Background</h2>
                <label for="qualifications">Qualifications/Education:</label>
                <textarea id="qualifications" name="qualifications" rows="4" required></textarea>
                <label for="experience">Experience:</label>
                <textarea id="experience" name="experience" rows="4" required></textarea>
                <button type="submit">Create Profile</button>
            </form>
        </div>




        <!-- MODIFIED: Organisational Management Section -->
        <section id="organisational-objectives" class="section-content" style="display:none;">

            <!-- Existing content for this section -->
             <div class="row">
                <div class="col-md-6">
                    <div id="objectives-container"></div>

                    <h2>Organisational Objectives</h2>
                    <button id="add-objective-btn">Add Objective</button>

        
                            <div id="objective-modal" class="modal">
                                <div class="modal-content">
                                    <span class="close">&times;</span>
                                    <form id="create-objective-form">
                                        <input type="text" placeholder="Objective Title" name="title" required />
                                        <textarea placeholder="Objective Description" name="description" required></textarea>
                                        <input type="number" placeholder="Objective Weight" name="weight" required />
                                        <button type="submit">Create Objective</button>
                                    </form>
                                </div>
                            </div>
                            <br>
                </div>

             </div>



             <div class="row">
                <div class="col-md-6">
            <h2>Organisational Management Settings</h2>
            
            <!-- NEW: Rank Management Form -->
            <div class="management-section">
                <h3>Gamification Rank Settings</h3>
                <form id="rank-settings-form">
                    <div id="rank-inputs-container">
                        <!-- Rank inputs will be dynamically added here by JS -->
                    </div>
                    <button type="button" id="add-rank-btn">Add Rank</button>
                    <button type="submit">Save Rank Settings</button>
                </form>
                <p id="rank-settings-status"></p>
            </div>
            </div>
        </div>
        </section>


        
        <section id="objectives-tree" class="section-content">
            <div id="tree-container">
                <!-- All objectives will be shown here in a tree -->
            </div>
        </section>

 

        <!-- MODIFIED: Employee Overview Section -->
        <section id="employee-overview" class="section-content" style="display:none;">
            <h2>Employee Overview</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <div id="employee-search">
                        <input type="text" id="employee-search-input" placeholder="Search employees...">
                            <!-- Suggestions will be displayed here -->
                            <div id="employee-search-results"></div>
                    </div>
                    <div id="employee-info">
                    <!-- Employee information will be loaded here -->
                    </div>


                    
            <!-- NEW: Calendar Heatmap with Period Controls -->
            <div id="visualizer" class="visualization-container">
                <div class="vis-header">
                    <h3>Contribution Heatmap</h3>
                    <!-- <div id="heatmap-controls" class="vis-controls">
                        <button class="control-btn active" data-period="year">Year</button>
                        <button class="control-btn" data-period="month">Month</button>
                        <button class="control-btn" data-period="week">Week</button>
                        <button class="control-btn" data-period="day">Day</button>
                    </div> -->
                </div>
                <div id="calendar-heatmap-container">
                    </div>
                <!-- <div id="navigation-controls" style="display: none; text-align: center; margin-top: 1rem;">
                    <button id="prev-btn">Previous</button>
                    <span id="nav-title" style="margin: 0 1rem; font-weight: bold;"></span>
                    <button id="next-btn">Next</button>
                </div> -->
            </div>
                    
                
                    



                    <div id="employee-skills-chart">
                    <div id="skillsRadarChart" class="radar-container">

                    </div>
                
                    </div>

                </div>
            </div>

            


            <!-- NEW: Gamification Visualization Area -->
            
            <div id="gamification-container">
                <div class="row">
                    <div class="col-md-6">
                <h3>Rank Progression</h3>
                <!-- This container will hold either the donut loader or the 3D cone -->

                <div id="gamification-visual">
                    <!-- Donut loader will be generated here by JS -->
                </div>
                 <!-- The 3D cone canvas will be created here by JS -->
            </div>



            </div>
            </div>
        </section>


        <div id="task-assignment" class="section-content">
            <div id="task-assignment-tabs">
                <button data-tab="assign-tasks">Assign Tasks</button>
                <button data-tab="self-generated-tasks">Self-Generated Tasks</button>
            </div>

            <div id="assign-tasks" class="task-content active">
                <form id="assign-task-form">
                    <h2>Assign Task</h2>
                    <!-- <label for="task_id">Task ID:</label>
                    <input type="text" id="task_id" name="task_id" required> -->
                    <label for="task_title">Task Title:</label>
                    <input type="text" id="task_title" name="task_title" required>
                    <label for="task_description">Task Description:</label>
                    <textarea id="task_description" name="task_description" rows="4" required></textarea>
                    <label for="assignment_date">Assignment Date:</label>
                    <input type="date" id="assignment_date" name="assignment_date" value="<?php echo date('Y-m-d'); ?>" required>
                    <label for="expected_completion_date">Expected Completion Date:</label>
                    <input type="date" id="expected_completion_date" name="expected_completion_date" value="<?php echo date('Y-m-d'); ?>" required>
                    <label for="associated_objectives">Associated Objectives:</label>
                    <select id="associated_objectives" name="associated_objectives" required>
                        <!-- Options will be populated from the backend -->
                    </select>


                    <!-- KPA Section -->
                    <div class="kpa-section">
                        <h3>Key Performance Actions</h3>
                        <div id="kpa-container-assign">
                            <div class="kpa-input-group">
                                <input type="text" name="kpa_text[]" placeholder="Enter KPA" required>
                                <input type="date" name="kpa_date[]" required>
                                <button type="button" class="delete-kpa">Delete</button>
                            </div>
                        </div>
                        <button type="button" id="add-kpa-btn-assign" class="add-kpa-btn">Add KPA</button>
                    </div>

                    <button id="partial_submit" type="submit">Submit</button>
                </form>

                <div class="recommendations">
                    <h3>Recommendations</h3>
                    <ul>
                        <!-- Recommendations will be populated here -->
                        <li>
                            <input type="checkbox" id="staff_1">
                            <label for="staff_1">Staff ID 1 - 90% Match</label>
                        </li>
                        <li>
                            <input type="checkbox" id="staff_2">
                            <label for="staff_2">Staff ID 2 - 85% Match</label>
                        </li>
                        <!-- Add more recommendations as needed -->
                    </ul>
                </div>
            </div>

        <div id="self-generated-tasks" class="task-content">
            <form id="self-generated-task-form">
                <h2>Create Self-Generated Task</h2>
                <label for="self_task_title">Task Title:</label>
                <input type="text" id="self_task_title" name="task_title" required>

                <label for="self_task_description">Task Description:</label>
                <textarea id="self_task_description" name="task_description" rows="4" required></textarea>

                <label for="self_start_date">Start Date:</label>
                <input type="date" id="self_start_date" name="start_date" required>

                <label for="self_expected_completion_date">Expected Completion Date:</label>
                <input type="date" id="self_expected_completion_date" name="expected_completion_date" required>

                <label for="self_associated_objectives">Associated Objectives:</label>
                <select id="self_associated_objectives" name="associated_objective" required>
                    <!-- Options will be populated dynamically -->
                </select>

                <label for="self_participants">Number of Participants:</label>
                <input type="number" id="self_participants" name="participants" min="1" value="1" required>

                <!-- KPA Section -->
                <div class="kpa-section">
                    <h3>Key Performance Actions</h3>
                    <div id="kpa-container-self">
                        <div class="kpa-input-group">
                            <input type="text" name="kpa_text[]" placeholder="Enter KPA" required>
                            <input type="date" name="kpa_date[]" required>
                            <button type="button" class="delete-kpa">Delete</button>
                        </div>
                    </div>
                    <button type="button" id="add-kpa-btn-self" class="add-kpa-btn">Add KPA</button>
                </div>

                <button type="submit">Create Self-Generated Task</button>
            </form>

            <!-- Recommendations will be inserted here -->
            <div id="self-task-recommendations" class="recommendations" style="display: none;">
                <h3>Recommended Team Members</h3>
                <ul>
                    <!-- Team member recommendations will be inserted here -->
                </ul>
            </div>
        </div>

        </div>

        <!-- Add a new section for task approval -->
        <div id="task-approval" class="section-content">
            <h2>Tasks Pending Approval</h2>
            <div id="tasks-for-approval-container">
                <!-- Tasks pending approval will be displayed here -->
            </div>
        </div>

        <div id="task-tracking" class="section-content">
            <h2>Task Progress</h2>
            <!-- The new animated trackers will be dynamically inserted here -->
            <div id="animated-task-tracker-container"></div> 
            
        </div>

        <!-- Add a modal for task approval -->
        <div id="task-approval-modal" class="modal">
            <div class="modal-content">
                <span class="close" id="approval-close">&times;</span>
                <h2>Task Approval</h2>
                <div id="task-approval-details">
                    <!-- Task details will be displayed here -->
                </div>
                <form id="task-approval-form">
                    <input type="hidden" id="approval-task-id" name="task_id">
                    <div class="approval-buttons">
                        <button type="button" id="approve-task-btn" data-decision="approve">Approve</button>
                        <button type="button" id="reject-task-btn" data-decision="reject">Reject</button>
                    </div>
                    <label for="approval-comments">Comments:</label>
                    <textarea id="approval-comments" name="comments" rows="4"></textarea>
                    <button type="submit">Submit Decision</button>
                </form>
            </div>
        </div>


    </section>

</main>


<!-- Draft Submission Modal -->
<div id="draft-modal" class="modal">
    <div class="modal-content">
        <span class="close" id="draft-close">&times;</span>
        <h2>Submit Draft</h2>
        <form id="draft-form">
            <div id="draft-history"></div>
            <textarea name="draft-content" placeholder="Enter your draft content" required></textarea>
            <input type="file" name="draft-file">
            <button type="submit">Submit Draft</button>
        </form>
    </div>
</div>



<!-- Review Modal -->
<div id="review-modal" class="modal">
    <div class="modal-content">
        <span class="close" id="review-close">&times;</span>
        <h2>Review Draft</h2>
        <form id="review-form">
            <div id="review-content"></div>
            <select name="review-status" required>
                <option value="Approved">Approve</option>
                <option value="Revisions Requested">Request Revisions</option>
            </select>
            <textarea name="review-comments" placeholder="Enter review comments"></textarea>
            <button type="submit">Submit Review</button>
        </form>
    </div>
</div>



<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.18.1/moment.min.js" charset="utf-8"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3/4.10.2/d3.min.js" charset="utf-8"></script>
<script src="/static/assets/cal/dist/calendar-heatmap.min.js"></script>


<!-- For the 3D cone visualization -->
<script src="https://cdn.jsdelivr.net/npm/three@0.128.0/build/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>




<!-- Additional scripts -->
<script src="/static/assets/hr.js"></script>


</body>
</html>